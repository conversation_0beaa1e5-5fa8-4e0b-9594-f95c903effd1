import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import { SYSTEM } from 'src/shared/constants/common.constant';
import { User } from 'src/shared/entities/auth/user.entity';
import { Brand, BrandDocument } from 'src/shared/entities/brand.entity';
import { extractSubdomain } from 'src/shared/utils/common.util';
import {
  BrandResponseDTO,
  BrandResponseListDTO,
  CreateBrandDto,
  GetBrandsDto,
} from './dto/brand.dto';

@Injectable()
export class BrandService {
  constructor(
    @InjectModel(Brand.name, AdminDB)
    private readonly brandModel: Model<BrandDocument>,
  ) {}

  async getAllBrands(
    getBrandsDto: GetBrandsDto,
  ): Promise<BrandResponseListDTO> {
    const { limit = 10, skip = 0 } = getBrandsDto;

    const [brands, total] = await Promise.all([
      this.brandModel.find().limit(Number(limit)).skip(Number(skip)).lean(),
      this.brandModel.countDocuments(),
    ]);

    const data = brands.map((brand) =>
      plainToInstance(BrandResponseDTO, brand, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async checkBrandExists(brand: string): Promise<{
    success: boolean;
  }> {
    const brandExists = await this.brandModel.exists({
      brand: extractSubdomain(brand),
    });
    return { success: !!brandExists };
  }

  async createBrand(
    createBrandDto: CreateBrandDto,
    authUser?: User,
  ): Promise<BrandResponseDTO[]> {
    try {
      const brands = createBrandDto.brands.map((brand) =>
        extractSubdomain(brand),
      );

      const existingBrands = await this.brandModel
        .find({ brand: { $in: brands } })
        .select('brand')
        .lean();

      const existingBrandNames = existingBrands.map((brand) => brand.brand);

      const newBrands = brands.filter(
        (brand) => !existingBrandNames.includes(brand),
      );

      const now = new Date();
      const createdBrands = await this.brandModel.insertMany(
        newBrands.map((brand) => ({
          brand,
          createdBy: authUser ? authUser.telegramId : SYSTEM,
          createdByUsername: authUser ? authUser.username : 'system',
          createdAt: now,
        })),
      );

      const data = createdBrands.map((brand) =>
        plainToInstance(BrandResponseDTO, brand, {
          excludeExtraneousValues: true,
        }),
      );

      return data;
    } catch (error) {
      console.error('Error creating brand:', error);
      throw new BadRequestException('Error creating brand', error.message);
    }
  }

  async deleteBrand(brand: string) {
    try {
      const brandExists = await this.brandModel.exists({
        brand: extractSubdomain(brand),
      });
      if (!brandExists) {
        throw new Error('Brand not found');
      }
      await this.brandModel.deleteOne({ brand: extractSubdomain(brand) });

      return { success: true };
    } catch (error) {
      console.error('Error deleting brand:', error);
      throw new BadRequestException('Error deleting brand', error.message);
    }
  }
}
