import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { BrandService } from './brand.service';
import {
  BrandResponseDTO,
  BrandResponseListDTO, CreateBrandDto,
  GetBrandsDto
} from './dto/brand.dto';

@ApiTags('Client Brands')
@Controller('client/brands')
@UseGuards(GetEmailGuard)
export class ClientBrandController {
  constructor(private readonly brandService: BrandService) {}

  @Post('/add')
  async addBrands(@Body() body: CreateBrandDto): Promise<BrandResponseDTO[]> {
    return this.brandService.createBrand(body);
  }

  // @Get('/check')
  // async checkBrandExists(
  //   @Query() query: CheckBrandExistsDto,
  // ): Promise<{ success: boolean }> {
  //   return this.brandService.checkBrandExists(query.brand);
  // }

  @Get('')
  async getBrands(@Query() query: GetBrandsDto): Promise<BrandResponseListDTO> {
    return this.brandService.getAllBrands(query);
  }
}
