import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  Min,
  Max,
} from 'class-validator';

export class CreateBrandDto {
  @ApiProperty({
    description: 'Array of brand names to create',
    example: ['brand1', 'brand2', 'brand3'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  brands: string[];
}

export class CheckBrandExistsDto {
  @ApiProperty({
    description: 'Brand name to check existence',
    example: 'mybrand',
  })
  @IsString()
  @IsNotEmpty()
  brand: string;
}

export class GetBrandsDto {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

export class BrandResponseDTO {
  @ApiProperty({
    description: 'Brand unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Brand name',
    example: 'mybrand',
  })
  @Expose()
  brand: string;

  @ApiProperty({
    description: 'Username of user who created this brand',
    example: 'john_doe',
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @ApiProperty({
    description: 'Brand creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;
}

export class BrandResponseListDTO {
  @ApiProperty({
    description: 'Array of brands',
    type: [BrandResponseDTO],
  })
  @Expose()
  data: BrandResponseDTO[];

  @ApiProperty({
    description: 'Total number of brands',
    example: 15,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class DeleteBrandDTO {
  @ApiProperty({
    description: 'Brand name to delete',
    example: 'mybrand',
  })
  @IsString()
  @IsNotEmpty()
  brand: string;
}
