import {
  Body,
  Controller,
  Delete,
  Get,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { Brand } from 'src/shared/entities/brand.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { BrandService } from './brand.service';
import {
  BrandResponseDTO,
  BrandResponseListDTO,
  CheckBrandExistsDto,
  CreateBrandDto,
  DeleteBrandDTO,
  GetBrandsDto,
} from './dto/brand.dto';

@ApiTags('Brands')
@Controller('brands')
export class BrandController {
  constructor(private readonly brandService: BrandService) {}

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post('/add')
  @ApiOperation({
    summary: 'Add brands',
    description: 'Add new brands to the system',
  })
  @ApiBody({
    description: 'Brand data',
    type: CreateBrandDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Brands added successfully',
    type: [Brand],
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async addBrands(
    @Body() body: CreateBrandDto,
    @RequestUser() user: User,
  ): Promise<BrandResponseDTO[]> {
    return this.brandService.createBrand(body, user);
  }

  @UseGuards(GetEmailGuard)
  @Get('/check')
  @ApiOperation({
    summary: 'Check brand exists',
    description: 'Check if a brand already exists in the system',
  })
  @ApiResponse({
    status: 200,
    description: 'Brand check completed',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid brand name',
  })
  async checkBrandExists(
    @Query() query: CheckBrandExistsDto,
  ): Promise<{ success: boolean }> {
    return this.brandService.checkBrandExists(query.brand);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('')
  @ApiOperation({
    summary: 'Get brands',
    description: 'Retrieve a paginated list of brands with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Brands retrieved successfully',
    type: BrandResponseListDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getBrands(@Query() query: GetBrandsDto): Promise<BrandResponseListDTO> {
    return this.brandService.getAllBrands(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Delete()
  @ApiOperation({
    summary: 'Delete brand',
    description: 'Soft delete a brand from the system',
  })
  @ApiResponse({
    status: 200,
    description: 'Brand deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Brand not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteBrand(
    @Query() query: DeleteBrandDTO,
  ): Promise<{ success: boolean }> {
    return this.brandService.deleteBrand(query.brand);
  }
}
