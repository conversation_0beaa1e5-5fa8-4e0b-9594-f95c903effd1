import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import { Brand, BrandSchema } from 'src/shared/entities/brand.entity';
import { AuthModule } from '../auth/auth.module';
import { BrandController } from './brand.controller';
import { BrandService } from './brand.service';
import { ClientBrandController } from './client-brand.controller';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Brand.name,
          schema: BrandSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [BrandController, ClientBrandController],
  providers: [BrandService],
  exports: [BrandService],
})
export class BrandModule {}
