import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ROLE } from 'src/shared/constants/auth.constant';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { Roles } from 'src/shared/decorators/role.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { RolesGuard } from 'src/shared/guards/role.guard';
import { DomainsService } from './domains.service';
import { CreateDomainDTO } from './dto/domain.dto';
import {
  DomainDetailResponseDTO,
  DomainResponseDTO,
  GetDomainsQueryDTO,
  GetDomainsResponseDTO,
} from './dto/get-domains.dto';

@ApiTags('Domains')
@ApiBearerAuth('JWT-auth')
@UseGuards(AuthGuard)
@Controller('domains')
export class DomainsController {
  constructor(private readonly domainsService: DomainsService) {}

  @Post('create')
  async createDomain(
    @Body() createDomainDTO: CreateDomainDTO,
    @RequestUser() user: User,
  ): Promise<DomainResponseDTO> {
    return this.domainsService.createDomain(createDomainDTO, user);
  }

  @Get('')
  async getPages(
    @Query() query: GetDomainsQueryDTO,
    @RequestUser() user: User,
  ): Promise<GetDomainsResponseDTO> {
    return this.domainsService.getDomains(query);
  }

  @Get('detail/:domainId')
  async getDetailPage(
    @Param('domainId') domainId: string,
    @RequestUser() user: User,
  ): Promise<DomainDetailResponseDTO> {
    return this.domainsService.getDomainById(domainId);
  }

  @Patch(':domainId')
  async updatePage(
    @Param('domainId') domainId: string,
    @Body() createDomainDTO: CreateDomainDTO,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.domainsService.updateDomain(domainId, createDomainDTO, user);
    return { success: true };
  }

  @UseGuards(RolesGuard)
  @Roles(ROLE.ADMIN, ROLE.SUPER_ADMIN)
  @Delete(':domainId')
  async deletePage(
    @Param('domainId') domainId: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.domainsService.deleteDomain(domainId, user);
    return { success: true };
  }
}
