import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToClass } from 'class-transformer';
import { isEmpty } from 'lodash';
import { Model, Types } from 'mongoose';
import { AdminDB } from 'src/config';
import { User } from 'src/shared/entities/auth/user.entity';
import { Domain, DomainDocument } from 'src/shared/entities/domain.entity';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import { CreateDomainDTO } from './dto/domain.dto';
import {
  DomainDetailResponseDTO,
  DomainResponseDTO,
  GetDomainsQueryDTO,
  GetDomainsResponseDTO,
} from './dto/get-domains.dto';

@Injectable()
export class DomainsService {
  constructor(
    @InjectModel(Domain.name, AdminDB)
    private readonly domainModel: Model<DomainDocument>,
  ) {}

  async createDomain(
    createDomainDTO: CreateDomainDTO,
    authUser: User,
  ): Promise<DomainResponseDTO> {
    const existingDomain = await this.domainModel
      .findOne({
        name: createDomainDTO.name,
      })
      .exec();
    if (existingDomain) {
      throw new BadRequestException('Domain already exists');
    }

    try {
      const domain = await this.domainModel.create({
        ...createDomainDTO,
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        createdAt: new Date(),
      });

      return plainToClass(DomainResponseDTO, domain, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log('Error creating domain:', error);
      throw new BadRequestException('Error creating domain. Please try again.');
    }
  }

  async getDomainById(id: string): Promise<DomainDetailResponseDTO> {
    try {
      const domain = await this.domainModel
        .aggregate([
          {
            $match: {
              _id: new Types.ObjectId(id),
            },
          },
          {
            $lookup: {
              from: 'pages',
              let: { localField: '$_id' },
              pipeline: [
                {
                  $addFields: {
                    domainIdObject: { $toObjectId: '$domainId' },
                  },
                },
                {
                  $match: {
                    $expr: { $eq: ['$$localField', '$domainIdObject'] },
                  },
                },
              ],
              as: 'pages',
            },
          },
        ])
        .exec();
      if (isEmpty(domain)) {
        throw new BadRequestException('Domain not found');
      }
      return plainToClass(DomainDetailResponseDTO, domain[0], {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log('Error fetching domain:', error);
      throw new BadRequestException(
        'Error fetching domain. Please try again.',
        error.message,
      );
    }
  }

  async updateDomain(
    id: string,
    updateDomainDTO: CreateDomainDTO,
    authUser: User,
  ): Promise<void> {
    try {
      const domain = await this.domainModel.findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
        },
        {
          ...updateDomainDTO,
          updatedBy: authUser.telegramId,
          updatedAt: new Date(),
        },
        { new: true, authUser },
      );

      if (!domain) {
        throw new BadRequestException('Domain not found');
      }
    } catch (error) {
      console.log('Error updating domain:', error);
      throw new BadRequestException('Error updating domain. Please try again.');
    }
  }

  async deleteDomain(id: string, authUser: User): Promise<void> {
    try {
      await this.domainModel.findOneAndUpdate(
        { _id: new Types.ObjectId(id) },
        {
          deletedBy: authUser.telegramId,
          deletedAt: new Date(),
        },
        {},
      );
    } catch (error) {
      console.log('Error deleting domain:', error);
      throw new BadRequestException(
        'Error deleting domain. Please try again.',
        error.message,
      );
    }
  }

  async getDomains(
    getDomainsQueryDTO: GetDomainsQueryDTO,
  ): Promise<GetDomainsResponseDTO> {
    try {
      const { skip = 0, limit = 10, search = '' } = getDomainsQueryDTO;

      const query = {
        $or: [
          { name: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ],
      };

      const [domains, total] = await Promise.all([
        this.domainModel
          .aggregate([
            { $match: query },
            {
              $lookup: {
                from: 'pages',
                let: { localField: '$_id' },
                pipeline: [
                  {
                    $addFields: {
                      domainIdObject: { $toObjectId: '$domainId' },
                    },
                  },
                  {
                    $match: {
                      $expr: { $eq: ['$$localField', '$domainIdObject'] },
                    },
                  },
                ],
                as: 'pages',
              },
            },
            {
              $addFields: {
                totalPages: { $size: '$pages' },
              },
            },
            {
              $sort: { createdAt: -1 },
            },
            { $skip: Number(skip) },
            { $limit: Number(limit) },
          ])
          .exec(),
        this.domainModel.countDocuments(query),
      ]);

      const transformedDomains = domains.map((domain) =>
        plainToClass(DomainResponseDTO, domain, {
          excludeExtraneousValues: true,
        }),
      );

      return {
        data: transformedDomains,
        total,
        skip: Number(skip),
        limit: Number(limit),
      };
    } catch (error) {
      console.log('Error fetching all domains:', error);
      throw new BadRequestException(
        'Error fetching all domains. Please try again.',
        error.message,
      );
    }
  }
}
