import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsN<PERSON>ber, Min, Max } from 'class-validator';
import { PageResponseDTO } from 'src/modules/pages/dto/get-pages.dto';

export class GetDomainsQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Search domains by name or description',
    example: 'example.com',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by creator ID',
    example: '*********',
  })
  @IsString()
  @IsOptional()
  createdBy?: string;
}

export class DomainResponseDTO {
  @ApiProperty({
    description: 'Domain unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Domain name',
    example: 'example.com',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Domain description',
    example: 'Main company domain',
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Total number of pages in this domain',
    example: 5,
  })
  @Expose()
  totalPages: number;

  @ApiProperty({
    description: 'Domain creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'Last update date',
    example: '2023-01-02T00:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;
}

export class GetDomainsResponseDTO {
  @ApiProperty({
    description: 'Array of domains',
    type: [DomainResponseDTO],
  })
  @Expose()
  data: Array<DomainResponseDTO>;

  @ApiProperty({
    description: 'Total number of domains matching the query',
    example: 25,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class DomainDetailResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  @Type(() => PageResponseDTO)
  pages: Array<PageResponseDTO>;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt?: Date;
}
