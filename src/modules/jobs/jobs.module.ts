import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  JobApplicant,
  JobApplicantSchema,
} from 'src/shared/entities/job-applicant.entity';
import { Job, JobSchema } from 'src/shared/entities/job.entity';
import { Page, PageSchema } from 'src/shared/entities/page.entity';
import { AuthModule } from '../auth/auth.module';
import { JobsController } from './jobs.controller';
import { JobsService } from './jobs.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Job.name,
          schema: JobSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
        {
          name: Page.name,
          schema: PageSchema,
        },
        {
          name: JobApplicant.name,
          schema: JobApplicantSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [JobsController],
  providers: [JobsService],
  exports: [JobsService],
})
export class JobsModule {}
