import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JOB_STATUS } from 'src/shared/constants/job.constant';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { Job } from 'src/shared/entities/job.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { CreateJobDTO } from './dto/create-job.dto';
import { EditJobDTO } from './dto/edit-job.dto';
import {
  GetJobsQueryDTO,
  GetJobsResponseDTO,
  JobDetailResponseDTO,
} from './dto/get-jobs.dto';
import { JobsService } from './jobs.service';

@ApiTags('Jobs')
@Controller('jobs')
export class JobsController {
  constructor(private readonly jobsService: JobsService) {}

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post('create')
  async createJob(
    @Body() body: CreateJobDTO,
    @RequestUser() user: User,
  ): Promise<Job> {
    const data = this.jobsService.createJob(body, user);
    return data;
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post('edit/:id')
  async edit(
    @Body() body: EditJobDTO,
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.jobsService.editJob(id, body, user);
    return { success: true };
  }

  @UseGuards(GetEmailGuard)
  @Get('')
  async getJobs(@Query() query: GetJobsQueryDTO): Promise<GetJobsResponseDTO> {
    const data = this.jobsService.getJobs(query);
    return data;
  }

  @Get(':id')
  async getJob(@Param('id') id: string): Promise<JobDetailResponseDTO> {
    const data = this.jobsService.getDetail(id);
    return data;
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Patch(':jobId/update-status')
  async updateStatus(
    @Param('jobId') jobId: string,
    @Body() { status }: { status: JOB_STATUS },
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.jobsService.updateJobStatus(jobId, status, user);
    return { success: true };
  }

  // @UseGuards(AuthGuard)
  // @Delete(':id')
  // async deleteJob(
  //   @Param('id') id: string,
  //   @RequestUser() user: User,
  // ): Promise<{ success: boolean }> {
  //   await this.jobsService.deleteJob(id, user);
  //   return { success: true };
  // }
}
