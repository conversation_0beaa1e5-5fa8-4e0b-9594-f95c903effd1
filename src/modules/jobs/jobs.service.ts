import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isNil } from 'lodash';
import { Model, Types } from 'mongoose';
import { AdminDB } from 'src/config';
import { Job, JobDocument } from 'src/shared/entities/job.entity';
import { User } from 'src/shared/entities/auth/user.entity';
import { CreateJobDTO } from './dto/create-job.dto';
import { EditJobDTO } from './dto/edit-job.dto';
import {
  GetJobsQueryDTO,
  GetJobsResponseDTO,
  JobDetailResponseDTO,
  JobResponseDTO,
} from './dto/get-jobs.dto';
import { plainToClass } from 'class-transformer';
import { Page, PageDocument } from 'src/shared/entities/page.entity';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import {
  JobApplicant,
  JobApplicantDocument,
} from 'src/shared/entities/job-applicant.entity';
import { ROLE } from 'src/shared/constants/auth.constant';
import { JOB_STATUS } from 'src/shared/constants/job.constant';

@Injectable()
export class JobsService {
  constructor(
    @InjectModel(Job.name, AdminDB)
    private readonly jobModel: AuthModel<JobDocument>,
    @InjectModel(JobApplicant.name, AdminDB)
    private readonly jobApplicantModel: AuthModel<JobApplicantDocument>,
    @InjectModel(Page.name, AdminDB)
    private readonly pageModel: Model<PageDocument>,
  ) {}

  async createJob(createJobDTO: CreateJobDTO, authUser: User): Promise<Job> {
    const page = await this.pageModel.findOne({
      code: createJobDTO.pageCode,
    });
    if (isNil(page)) {
      throw new BadRequestException('Page not found');
    }

    try {
      return this.jobModel.create({
        ...createJobDTO,
        pageId: page._id,
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        createdAt: new Date(),
      });
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error create job', error.message);
    }
  }

  async editJob(
    id: string,
    editJobDTO: EditJobDTO,
    authUser: User,
  ): Promise<void> {
    const { pageId } = editJobDTO;

    try {
      const job = await this.jobModel.findOneAndUpdateWithAuth(
        {
          _id: new Types.ObjectId(id),
          deletedAt: null,
        },
        {
          ...editJobDTO,
          pageId: pageId ? new Types.ObjectId(pageId) : undefined,
          updatedBy: authUser.telegramId,
          updatedAt: new Date(),
        },
        {},
        authUser,
      );
      if (isNil(job)) {
        throw new BadRequestException('Job not found');
      }
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error update job', error.message);
    }
  }

  async getJobs(query: GetJobsQueryDTO): Promise<GetJobsResponseDTO> {
    const {
      skip = 0,
      limit = 10,
      search,
      pageId,
      createdBy,
      pageCode,
      status,
    } = query;

    const filter = {};

    if (search) {
      filter['$or'] = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
      ];
    }
    if (createdBy) {
      filter['createdBy'] = createdBy;
    }
    if (pageCode) {
      const page = await this.pageModel.findOne({ code: pageCode });
      if (isNil(page)) {
        throw new BadRequestException('Page not found');
      }
      filter['pageId'] = page._id;
    } else if (pageId) {
      filter['pageId'] = new Types.ObjectId(pageId);
    }
    if (status) {
      filter['status'] = status;
    }

    const [data, total] = await Promise.all([
      this.jobModel
        .aggregate([
          {
            $match: filter,
          },
          {
            $lookup: {
              from: 'pages',
              let: { pageId: { $toObjectId: '$pageId' } },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$_id', '$$pageId'] },
                  },
                },
              ],
              as: 'page',
            },
          },
          {
            $unwind: {
              path: '$page',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'job_applicants',
              let: { localField: '$_id' },
              pipeline: [
                {
                  $addFields: {
                    jobIdObject: { $toObjectId: '$jobId' },
                  },
                },
                {
                  $match: {
                    $expr: { $eq: ['$$localField', '$jobIdObject'] },
                  },
                },
              ],
              as: 'jobApplicants',
            },
          },
          {
            $addFields: {
              totalApplicants: { $size: '$jobApplicants' },
            },
          },
          { $sort: { createdAt: -1 } },
          { $skip: Number(skip) },
          { $limit: Number(limit) },
        ])
        .exec(),
      this.jobModel.countDocuments(filter),
    ]);

    return {
      data: data.map((job) =>
        plainToClass(JobResponseDTO, job, { excludeExtraneousValues: true }),
      ),
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async getDetail(id: string): Promise<JobDetailResponseDTO> {
    const job = await this.jobModel
      .aggregate([
        {
          $match: {
            _id: new Types.ObjectId(id),
            status: JOB_STATUS.ACTIVATED,
          },
        },
        {
          $lookup: {
            from: 'pages',
            let: { pageId: { $toObjectId: '$pageId' } },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$pageId'] },
                },
              },
            ],
            as: 'page',
          },
        },
        {
          $unwind: {
            path: '$page',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'job_applicants',
            let: { localField: '$_id' },
            pipeline: [
              {
                $addFields: {
                  jobIdObject: { $toObjectId: '$jobId' },
                },
              },
              {
                $match: {
                  $expr: { $eq: ['$$localField', '$jobIdObject'] },
                },
              },
            ],
            as: 'jobApplicants',
          },
        },
      ])
      .exec();
    if (isNil(job[0])) {
      throw new BadRequestException('Job not found');
    }
    return plainToClass(JobDetailResponseDTO, job[0], {
      excludeExtraneousValues: true,
    });
  }

  async deleteJob(id: string, authUser: User): Promise<void> {
    try {
      const applicants = await this.jobApplicantModel.find({
        jobId: id,
        deletedAt: null,
      });

      if (authUser.role !== ROLE.ADMIN && applicants.length > 0) {
        throw new ForbiddenException('Cannot delete job with applicants');
      }

      const job = await this.jobModel.findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
        },
        {
          deletedAt: new Date(),
          deletedBy: authUser.telegramId,
        },
      );

      if (isNil(job)) {
        throw new BadRequestException('Job not found');
      }
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error deleting job');
    }
  }

  async updateJobStatus(
    id: string,
    status: string,
    authUser: User,
  ): Promise<void> {
    try {
      const job = await this.jobModel.findOneAndUpdateWithAuth(
        {
          _id: new Types.ObjectId(id),
        },
        {
          status,
          updatedBy: authUser.telegramId,
          updatedAt: new Date(),
        },
        {
          new: true,
        },
        authUser,
      );

      if (isNil(job)) {
        throw new BadRequestException('Job not found');
      }
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error updating job status', error.message);
    }
  }
}
