import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, plainToClass, Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsNumber,
  Min,
  Max,
  IsEnum,
} from 'class-validator';
import { ApplicantResponseDTO } from 'src/modules/applicants/dto/get-applicants.dto';
import { PageResponseDTO } from 'src/modules/pages/dto/get-pages.dto';
import { JOB_STATUS } from 'src/shared/constants/job.constant';

export class GetJobsQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Search jobs by title, description, or content',
    example: 'developer',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by creator ID',
    example: '123456789',
  })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Filter by page ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  pageId?: string;

  @ApiPropertyOptional({
    description: 'Filter by page code',
    example: 'careers-page',
  })
  @IsString()
  @IsOptional()
  pageCode?: string;

  @ApiPropertyOptional({
    description: 'Filter by job status',
    enum: JOB_STATUS,
    example: JOB_STATUS.ACTIVATED,
  })
  @IsOptional()
  @IsEnum(JOB_STATUS)
  status?: JOB_STATUS;
}

export class JobResponseDTO {
  @ApiProperty({
    description: 'Job unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Developer',
  })
  @Expose()
  title: string;

  @ApiProperty({
    description: 'Job description',
    example: 'We are looking for an experienced developer...',
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Job detailed content',
    example: 'Full job description with requirements...',
  })
  @Expose()
  content: string;

  @ApiProperty({
    description: 'Job location address',
    example: '123 Main St, City, Country',
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Total number of applicants for this job',
    example: 25,
  })
  @Expose()
  totalApplicants: number;

  @ApiProperty({
    description: 'Job status',
    enum: JOB_STATUS,
    example: JOB_STATUS.ACTIVATED,
  })
  @Expose()
  status: JOB_STATUS;

  @ApiProperty({
    description: 'Associated page information',
    type: () => PageResponseDTO,
  })
  @Expose()
  @Type(() => PageResponseDTO)
  page: PageResponseDTO;

  @ApiProperty({
    description: 'Job creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'ID of user who created this job',
    example: 123456789,
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Last update date',
    example: '2023-01-02T00:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;

  @ApiPropertyOptional({
    description: 'ID of user who last updated this job',
    example: 123456789,
  })
  @Expose()
  updatedBy?: number;
}

export class GetJobsResponseDTO {
  @ApiProperty({
    description: 'Array of jobs',
    type: [JobResponseDTO],
  })
  @Expose()
  @Type(() => JobResponseDTO)
  data: Array<JobResponseDTO>;

  @ApiProperty({
    description: 'Total number of jobs matching the query',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class JobDetailResponseDTO {
  @ApiProperty({
    description: 'Job unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Developer',
  })
  @Expose()
  title: string;

  @ApiProperty({
    description: 'Job description',
    example: 'We are looking for an experienced developer...',
  })
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Job detailed content',
    example: 'Full job description with requirements...',
  })
  @Expose()
  content: string;

  @ApiProperty({
    description: 'Job status',
    enum: JOB_STATUS,
    example: JOB_STATUS.ACTIVATED,
  })
  @Expose()
  status: JOB_STATUS;

  @ApiProperty({
    description: 'Job location address',
    example: '123 Main St, City, Country',
  })
  @Expose()
  address: string;

  @ApiProperty({
    description: 'List of applicants for this job',
    type: [ApplicantResponseDTO],
  })
  @Expose()
  @Transform(({ obj }) => {
    return obj.jobApplicants.map((applicant) =>
      plainToClass(ApplicantResponseDTO, applicant, {
        excludeExtraneousValues: true,
      }),
    );
  })
  applicants: Array<ApplicantResponseDTO>;

  @ApiProperty({
    description: 'Associated page ID',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj.pageId.toString())
  pageId: string;

  @ApiProperty({
    description: 'Job creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'ID of user who created this job',
    example: 123456789,
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Last update date',
    example: '2023-01-02T00:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;

  @ApiPropertyOptional({
    description: 'ID of user who last updated this job',
    example: 123456789,
  })
  @Expose()
  updatedBy?: number;
}
