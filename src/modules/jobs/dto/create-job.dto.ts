import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsArray } from 'class-validator';

export class CreateJobDTO {
  @ApiProperty({
    description: 'Job title',
    example: 'Senior Software Developer',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiPropertyOptional({
    description: 'Job description',
    example: 'We are looking for an experienced developer...',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Job detailed content',
    example:
      'Full job description with requirements, responsibilities, and benefits...',
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Job location address',
    example: '123 Main St, City, Country',
  })
  @IsString()
  @IsNotEmpty()
  address: string;

  @ApiProperty({
    description: 'Page code where this job will be displayed',
    example: 'careers-page',
  })
  @IsString()
  @IsNotEmpty()
  pageCode: string;

  @ApiPropertyOptional({
    description: 'Job tags for categorization',
    example: ['remote', 'full-time', 'senior'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}
