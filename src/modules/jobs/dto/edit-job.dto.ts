import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class EditJobDTO {
  @ApiPropertyOptional({
    description: 'Job title',
    example: 'Senior Software Developer',
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({
    description: 'Job description',
    example: 'We are looking for an experienced developer...',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Job detailed content',
    example:
      'Full job description with requirements, responsibilities, and benefits...',
  })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({
    description: 'Job location address',
    example: '123 Main St, City, Country',
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiPropertyOptional({
    description: 'Page ID where this job will be displayed',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  pageId?: string;

  @ApiPropertyOptional({
    description: 'Job tags for categorization',
    example: ['remote', 'full-time', 'senior'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}
