import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { isEmpty, isNil, uniq } from 'lodash';
import { Model, Types } from 'mongoose';
import { AdminDB } from 'src/config';
import { User } from 'src/shared/entities/auth/user.entity';
import {
  FileEmail,
  FileEmailDocument,
} from 'src/shared/entities/file/file-email.entity';
import { FileDocument } from 'src/shared/entities/file/file.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { ClerkUserService } from 'src/shared/services/clerk.service';
import { findDuplicates, normalizeDomain } from 'src/shared/utils/common.util';
import {
  AddManyEmailResponseDTO,
  AddManyEmailsDTO,
  DeleteEmailDTO,
  FileResponseDTO,
  GetEmailsQueryDTO,
  GetFileQueryDTO,
  GetFilesQueryDTO,
  GetFilesResponseDTO,
  UpdateEmailsDTO,
} from './dto/file.dto';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';
import {
  FileEmailData,
  FileEmailDTO,
  FileEmailResponseDto,
  UpdateFileEmailDto,
} from './dto/file-email.dto';

@Injectable()
export class FilesService {
  constructor(
    @InjectModel(File.name, AdminDB)
    private readonly fileModel: Model<FileDocument>,
    @InjectModel(FileEmail.name, AdminDB)
    private readonly fileEmailModel: Model<FileEmailDocument>,

    private readonly azureStorageService: AzureStorageService,
    private readonly clerkUserService: ClerkUserService,
  ) {}

  async addManyEmails(
    addManyEmailsDTO: AddManyEmailsDTO,
    authUser: User,
    file: Express.Multer.File,
    logo?: Express.Multer.File,
  ): Promise<AddManyEmailResponseDTO> {
    const { emails, prefixPass, domainName, tawktoId, expirationLink, popup } =
      addManyEmailsDTO;

    // Format input emails
    const formattedEmails = emails.map((email) =>
      this.detectEmailAndCountry(email),
    );

    const { duplicates: duplicateEmails, uniqueItems: uniqueEmails } =
      findDuplicates(formattedEmails, 'email');

    const uniqueEmailStrings = uniqueEmails.map((item) => item.email);
    const duplicateEmailStrings = duplicateEmails.map((item) => item.email);

    const duplicateEmailsInDb = await this.checkDuplicateEmails(
      uniqueEmailStrings,
      normalizeDomain(domainName),
    );
    const allDuplicateEmails = [
      ...duplicateEmailStrings,
      ...duplicateEmailsInDb,
    ];
    const uniqueEmailsToSave = uniqueEmails.filter(
      (email) => !allDuplicateEmails.includes(email.email),
    );

    try {
      // Upload files
      const fileUrl = await this.azureStorageService.uploadFile(file);
      const logoUrl = logo
        ? await this.azureStorageService.uploadFile(logo)
        : undefined;

      // Save file and logo information
      const fileDoc = await this.fileModel.create({
        file: fileUrl,
        logo: logoUrl,
        prefixPass,
        domainName: normalizeDomain(domainName),
        tawktoId,
        createdAt: new Date(),
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
      });

      // Save emails
      const now = new Date();
      const fileEmails = await Promise.all(
        uniqueEmailsToSave.map(async (email) => ({
          email: email.email,
          fileId: fileDoc._id,
          domainName: normalizeDomain(domainName),
          expirationLink,
          countryCode: email.countryCode,
          popup: !!popup,
          createdAt: now,
          createdBy: authUser.telegramId,
          createdByUsername: authUser.username,
          shortLinkId: await this.generateUniqueIdShortLink(),
          shortLinkStatus: SHORT_LINK_STATUS.ACTIVATED,
        })),
      );
      const insertedEmails = await this.fileEmailModel.insertMany(fileEmails);

      // Create users
      await Promise.all(
        uniqueEmailsToSave.map(async (email) => {
          await this.clerkUserService.createUser({
            emailAddress: [email.email],
          });
        }),
      );

      return {
        emails: insertedEmails.map((fileEmail) => ({
          email: fileEmail.email,
          shortLinkId: fileEmail.shortLinkId,
          shortLinkStatus: fileEmail.shortLinkStatus,
          countryCode: fileEmail.countryCode,
          popup: !!popup,
        })),
        duplicateEmails: uniq(allDuplicateEmails),
        file: fileDoc.file,
        logo: fileDoc.logo,
        prefixPass: fileDoc.prefixPass,
        tawktoId: fileDoc.tawktoId,
        domainName: normalizeDomain(domainName),
        createdAt: new Date(),
      };
    } catch (error) {
      console.error('Error adding emails:', error);
      throw new BadRequestException('Failed to add emails', error.message);
    }
  }

  async checkDuplicateEmails(
    emailList: string[],
    domainName: string,
  ): Promise<string[]> {
    const existingEmails = await this.fileEmailModel
      .find({
        email: { $in: emailList },
        domainName: normalizeDomain(domainName),
      })
      .select('email')
      .lean();

    return existingEmails.map((email) => email.email);
  }

  async getFileDetail(query: GetFileQueryDTO): Promise<FileResponseDTO> {
    const { email, domainName } = query;

    if (isNil(email) || isNil(domainName)) {
      throw new BadRequestException('Email or domainId not found');
    }

    const fileEmail = await this.fileEmailModel
      .findOne({
        email,
        domainName: new RegExp(normalizeDomain(domainName), 'i'),
      })
      .select('fileId expirationLink shortLinkId shortLinkStatus');
    if (isNil(fileEmail)) {
      throw new BadRequestException('Email not found');
    }

    const file = await this.fileModel
      .findOne({
        _id: fileEmail.fileId,
      })
      .lean();
    if (isNil(file)) {
      throw new BadRequestException('File not found');
    }

    return plainToInstance(
      FileResponseDTO,
      {
        ...file,
        email,
        expirationLink: fileEmail.expirationLink,
        shortLinkId: fileEmail.shortLinkId,
        shortLinkStatus: fileEmail.shortLinkStatus,
        shortLinkUrl: fileEmail.shortLinkUrl,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  async getEmails(query: GetEmailsQueryDTO): Promise<
    {
      email: string;
      shortLinkId: string;
      shortLinkStatus: SHORT_LINK_STATUS;
      countryCode: string;
    }[]
  > {
    const { fileId, shortLinkStatus, countryCode } = query;

    const queryCondition: Record<string, any> = {
      fileId: new Types.ObjectId(fileId),
    };
    if (!isNil(shortLinkStatus)) {
      queryCondition.shortLinkStatus = shortLinkStatus;
    }
    if (!isNil(countryCode)) {
      queryCondition.countryCode = countryCode;
    }

    const fileEmails = await this.fileEmailModel
      .find(queryCondition)
      .select('email shortLinkId shortLinkStatus countryCode')
      .lean();

    return fileEmails.map((fileEmail) => ({
      email: fileEmail.email,
      shortLinkId: fileEmail.shortLinkId,
      shortLinkStatus: fileEmail.shortLinkStatus,
      countryCode: fileEmail.countryCode,
      shortLinkUrl: fileEmail.shortLinkUrl,
    }));
  }

  async updateEmails(
    fileId: string,
    body: UpdateEmailsDTO,
    authUser: User,
  ): Promise<AddManyEmailResponseDTO> {
    try {
      const { emails } = body;

      if (isNil(emails) || isEmpty(emails)) {
        throw new BadRequestException('No emails provided');
      }

      if (isNil(fileId)) {
        throw new BadRequestException('File ID not found');
      }

      const file = await this.fileModel.findById({
        _id: new Types.ObjectId(fileId),
      });

      if (isNil(file)) {
        throw new BadRequestException('File not found');
      }

      const formattedEmails = emails.map((email) =>
        this.detectEmailAndCountry(email),
      );

      const { duplicates, uniqueItems: uniqueEmails } = findDuplicates(
        formattedEmails,
        'email',
      );

      const uniqueEmailStrings = uniqueEmails.map((item) => item.email);

      const existingEmails = await this.checkDuplicateEmails(
        uniqueEmailStrings,
        file.domainName,
      );

      const emailsToUpdate = uniqueEmails.filter(
        (email) => !existingEmails.includes(email.email),
      );
      const emailsToDelete = existingEmails.filter(
        (email) => !uniqueEmailStrings.includes(email),
      );

      const [deletedEmails, insertedEmails] = await Promise.all([
        this.fileEmailModel.deleteMany({ email: { $in: emailsToDelete } }),
        this.fileEmailModel.insertMany(
          emailsToUpdate.map((email) => ({
            email,
            fileId: file._id,
            domainName: file.domainName,
            createdAt: new Date(),
            createdBy: authUser.telegramId,
            createdByUsername: authUser.username,
          })),
        ),
      ]);

      return {
        emails: insertedEmails.map((fileEmail) => ({
          email: fileEmail.email,
          shortLinkId: fileEmail.shortLinkId,
          shortLinkStatus: fileEmail.shortLinkStatus,
          countryCode: fileEmail.countryCode,
          shortLinkUrl: fileEmail.shortLinkUrl,
        })),
        duplicateEmails: duplicates,
        file: file.file,
        logo: file.logo,
        prefixPass: file.prefixPass,
        tawktoId: file.tawktoId,
        domainName: file.domainName,
        createdAt: file.createdAt,
        updatedAt: new Date(),
      };
    } catch (error) {
      console.error('Error updating file email:', error);
      throw new BadRequestException(
        'Failed to update file email',
        error.message,
      );
    }
  }

  async getFiles(query: GetFilesQueryDTO): Promise<GetFilesResponseDTO> {
    const { skip = 0, limit = 10, domainName } = query;

    let queryCondition = {};

    if (!isNil(domainName)) {
      queryCondition['domainName'] = {
        $regex: new RegExp(normalizeDomain(normalizeDomain(domainName)), 'i'),
      };
    }

    const [files, total] = await Promise.all([
      this.fileModel
        .find(queryCondition)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 })
        .lean(),
      this.fileModel.countDocuments(queryCondition),
    ]);

    const data = await Promise.all(
      files.map(async (file) => {
        const [idsUsed, idsTotal] = await Promise.all([
          this.fileEmailModel.countDocuments({
            fileId: file._id,
            shortLinkStatus: SHORT_LINK_STATUS.DEACTIVATED,
          }),
          this.fileEmailModel.countDocuments({
            fileId: file._id,
          }),
        ]);
        return plainToInstance(
          FileResponseDTO,
          {
            ...file,
            percentIdsUsed: Math.round((idsUsed / idsTotal) * 100) || 0,
          },
          {
            excludeExtraneousValues: true,
          },
        );
      }),
    );

    return {
      data,
      total,
      limit: Number(limit),
      skip: Number(skip),
    };
  }

  async deleteEmail(body: DeleteEmailDTO): Promise<void> {
    try {
      const { emails, domainName } = body;

      if (emails.length == 0 || isNil(domainName)) {
        throw new BadRequestException('Email or domainId not found');
      }

      const deleteEmails = await this.fileEmailModel.deleteMany({
        email: { $in: emails },
        domainName: {
          $regex: new RegExp(normalizeDomain(domainName), 'i'),
        },
      });

      if (deleteEmails.deletedCount === 0) {
        throw new BadRequestException('No emails found to delete');
      }

      await Promise.all(
        emails.map(async (email) => {
          await this.clerkUserService.deleteUserByEmail(email);
        }),
      );
    } catch (error) {
      throw new BadRequestException('Failed to delete email', error.message);
    }
  }

  async downloadFile(
    fileId: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const file = await this.fileModel.findById(fileId);
      if (!file) {
        throw new BadRequestException('File not found');
      }

      return this.azureStorageService.downloadFile(file.file);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new BadRequestException('Failed to download file', error.message);
    }
  }

  private generateRandomString(length: number = 6): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async generateUniqueIdShortLink(): Promise<string> {
    const result = this.generateRandomString();

    const existing = await this.fileEmailModel.findOne({
      shortLinkId: result,
    });

    if (!isNil(existing)) {
      return this.generateUniqueIdShortLink();
    }

    return result;
  }

  async getFileEmails(
    fileEmailDto: FileEmailDTO,
  ): Promise<FileEmailResponseDto> {
    const { skip = 0, limit = 10, search, popup } = fileEmailDto;

    const query: Record<string, any> = {};

    if (search) {
      query.$or = [
        { email: { $regex: new RegExp(search, 'i') } },
        { shortLinkId: { $regex: new RegExp(search, 'i') } },
      ];
    }

    if (popup && !isEmpty(popup)) {
      if (popup === 'false') {
        query.$or = [{ popup: false }, { popup: { $exists: false } }];
      } else {
        query.popup = popup;
      }
    }

    const [fileEmails, total] = await Promise.all([
      this.fileEmailModel
        .find(query)
        .select('_id email domainName popup shortLinkId')
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .lean(),
      this.fileEmailModel.countDocuments(query),
    ]);

    const data = fileEmails.map((fileEmail) =>
      plainToInstance(FileEmailData, fileEmail, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: +skip,
      limit: +limit,
    };
  }

  async updateFileEmail(
    id: string,
    body: UpdateFileEmailDto,
    authUser: User,
  ): Promise<FileEmailData> {
    const { popup } = body;

    if (isNil(popup)) {
      throw new BadRequestException('Popup value is required');
    }

    const fileEmail = await this.fileEmailModel.findByIdAndUpdate(
      id,
      {
        popup,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      },
      { new: true },
    );

    if (!fileEmail) {
      throw new BadRequestException('File email not found');
    }

    return plainToInstance(FileEmailData, fileEmail, {
      excludeExtraneousValues: true,
    });
  }

  detectEmailAndCountry(input: string): { email: string; countryCode: string } {
    if (!input || typeof input !== 'string') {
      throw new BadRequestException('Invalid input format');
    }

    // Loại bỏ khoảng trắng thừa và tách chuỗi bằng ký tự "|"
    const [email, countryCode] = input.split('|').map((part) => part.trim());

    if (!email) {
      throw new BadRequestException('Input must contain email.');
    }

    return { email, countryCode: countryCode?.toUpperCase() };
  }

  async addShortLinkUrl(
    shortLinkId: string,
    shortLinkUrl: string,
    authUser: User,
  ): Promise<FileEmailData> {
    try {
      const fileEmail = await this.fileEmailModel.findOneAndUpdate(
        { shortLinkId },
        {
          shortLinkUrl,
          updatedAt: new Date(),
          updatedBy: authUser.telegramId,
        },
        { new: true },
      );

      if (!fileEmail) {
        throw new BadRequestException('File email not found');
      }

      return plainToInstance(FileEmailData, fileEmail, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.error('Error adding short link URL:', error);
      throw new BadRequestException(
        'Failed to add short link URL',
        error.message,
      );
    }
  }
}
