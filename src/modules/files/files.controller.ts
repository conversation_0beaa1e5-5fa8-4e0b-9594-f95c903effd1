import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { isNil } from 'lodash';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { FileEmailDTO, UpdateFileEmailDto } from './dto/file-email.dto';
import {
  AddManyEmailsDTO,
  DeleteEmailDTO,
  GetEmailsQueryDTO,
  GetFileQueryDTO,
  GetFilesQueryDTO,
  UpdateEmailsDTO,
} from './dto/file.dto';
import { FilesService } from './files.service';

@ApiTags('Files')
@Controller('files')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post('add-many')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'file', maxCount: 1 }, // Attachment file
      { name: 'logo', maxCount: 1 }, // Logo
    ]),
  )
  async addManyEmails(
    @Body() body: AddManyEmailsDTO,
    @RequestUser() user: User,
    @UploadedFiles()
    files: { file?: Express.Multer.File[]; logo?: Express.Multer.File[] },
  ) {
    const file = files.file?.[0];
    const logo = files.logo?.[0];
    if (isNil(file)) {
      throw new BadRequestException('File is required');
    }
    return this.filesService.addManyEmails(body, user, file, logo);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('/emails')
  async getEmails(@Query() query: GetEmailsQueryDTO) {
    return this.filesService.getEmails(query);
  }

  @UseGuards(GetEmailGuard)
  @Get('/get-detail')
  async getDetailFile(@Query() query: GetFileQueryDTO) {
    return this.filesService.getFileDetail(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Patch(':fileId')
  async updateFile(
    @Param('fileId') fileId: string,
    @Body() body: UpdateEmailsDTO,
    @RequestUser() user: User,
  ) {
    return this.filesService.updateEmails(fileId, body, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('get-files')
  async getFiles(@Query() query: GetFilesQueryDTO) {
    return this.filesService.getFiles(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('get-file-email')
  async getFileEmails(@Query() query: FileEmailDTO) {
    return this.filesService.getFileEmails(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Patch(':id/update-file-email')
  async updateFileEmail(
    @Param('id') id: string,
    @Body() body: UpdateFileEmailDto,
    @RequestUser() user: User,
  ) {
    return this.filesService.updateFileEmail(id, body, user);
  }

  @Get(':id/download')
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } = await this.filesService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'attachment');

    stream.pipe(res);
  }

  @UseGuards(GetEmailGuard)
  @Delete('delete-email')
  async deleteEmail(@Body() body: DeleteEmailDTO): Promise<{
    success: boolean;
  }> {
    await this.filesService.deleteEmail(body);

    return {
      success: true,
    };
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':shortLinkId/add-short-link-url')
  async addShortLinkUrl(
    @Param('shortLinkId') shortLinkId: string,
    @Body() body: { shortLinkUrl: string },
    @RequestUser() user: User,
  ) {
    const data = await this.filesService.addShortLinkUrl(
      shortLinkId,
      body.shortLinkUrl,
      user,
    );

    return data;
  }
}
