import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  FileEmail,
  FileEmailSchema,
} from 'src/shared/entities/file/file-email.entity';
import { File, FileSchema } from 'src/shared/entities/file/file.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { ClerkUserService } from 'src/shared/services/clerk.service';
import { AuthModule } from '../auth/auth.module';
import { FilesController } from './files.controller';
import { FilesService } from './files.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: File.name,
          schema: FileSchema,
        },
        {
          name: FileEmail.name,
          schema: FileEmailSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [FilesController],
  providers: [FilesService, AzureStorageService, ClerkUserService],
  exports: [FilesService],
})
export class FilesModule {}
