import { Expose, Transform, Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class FileEmailDTO {
  @IsString()
  @IsOptional()
  search?: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  popup?: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @Type(() => Number)
  @IsOptional()
  skip?: number;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  @Type(() => Number)
  @Min(1)
  @IsOptional()
  limit?: number;
}

export class FileEmailData {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  email: string;

  @Expose()
  shortLinkId: string;

  @Expose()
  shortLinkUrl: string;

  @Expose()
  domainName: string;

  @Expose()
  @Transform(({ value }) => (value === undefined ? false : value))
  popup: boolean;
}

export class FileEmailResponseDto {
  @Expose()
  @Type(() => FileEmailData)
  data: FileEmailData[];

  @Expose()
  total: number;

  @Expose()
  skip: number;

  @Expose()
  limit: number;
}

export class UpdateFileEmailDto {
  @Expose()
  popup: boolean;
}
