import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsOptional,
  IsString,
  <PERSON>N<PERSON>ber,
  Min,
  Max,
} from 'class-validator';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';
export class AddManyEmailsDTO {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  emails: string[];

  @IsString()
  @IsNotEmpty()
  prefixPass: string;

  @IsString()
  @IsNotEmpty()
  tawktoId: string;

  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  popup?: boolean;

  @IsString()
  @IsNotEmpty()
  domainName: string;

  @IsString()
  @IsOptional()
  @IsNotEmpty()
  expirationLink?: string;
}

export class AddManyEmailResponseDTO {
  @Expose()
  emails: {
    email: string;
    shortLinkId: string;
    shortLinkStatus: SHORT_LINK_STATUS;
  }[];

  @Expose()
  prefixPass: string;

  @Expose()
  file: string;

  @Expose()
  logo: string;

  @Expose()
  domainName: string;

  @Expose()
  tawktoId: string;

  @Expose()
  duplicateEmails: string[];

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt?: Date;
}

export class FileResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  email: string;

  @Expose()
  file: string;

  @Expose()
  logo: string;

  @Expose()
  prefixPass: string;

  @Expose()
  domainName: string;

  @Expose()
  expirationLink: string;

  @Expose()
  shortLinkId: string;

  @Expose()
  shortLinkStatus: SHORT_LINK_STATUS;

  @Expose()
  tawktoId: string;

  @Expose()
  percentIdsUsed: number;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}

export class GetFileQueryDTO {
  @IsString()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  domainName: string;
}

export class DeleteEmailDTO {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  emails: string[];

  @IsString()
  @IsNotEmpty()
  domainName: string;
}

export class GetEmailsQueryDTO {
  @IsString()
  @IsNotEmpty()
  fileId: string;

  @IsString()
  @IsOptional()
  shortLinkStatus?: SHORT_LINK_STATUS;

  @IsString()
  @IsOptional()
  countryCode?: string;
}

export class UpdateEmailsDTO {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  emails: string[];
}

export class GetFilesQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by domain name',
    example: 'example.com',
  })
  @IsString()
  @IsOptional()
  domainName?: string;
}

export class GetFilesResponseDTO {
  @ApiProperty({
    description: 'Array of files',
    type: [FileResponseDTO],
  })
  @Expose()
  @Type(() => FileResponseDTO)
  data: FileResponseDTO[];

  @ApiProperty({
    description: 'Total number of files',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}
