import {
  IsString,
  Val<PERSON><PERSON>Nested,
  <PERSON><PERSON>rray,
  IsOptional,
  IsN<PERSON>ber,
} from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { PartialType } from '@nestjs/mapped-types';
import { MailTemplate } from 'src/shared/entities/camp/mail-template.entity';

export class MailHTMLDto {
  @IsString()
  subject: string;

  @IsString()
  content: string;

  @IsOptional()
  @IsNumber()
  delay?: number;
}

export class CreateMailTemplateDto {
  @IsString()
  sender: string;

  @IsString()
  senderName: string;

  @ValidateNested()
  @Type(() => MailHTMLDto)
  startHTML: MailHTMLDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MailHTMLDto)
  otherHTML: MailHTMLDto[];

  @ValidateNested()
  @Type(() => MailHTMLDto)
  endHTML: MailHTMLDto;

  @IsString()
  brand: string;
}

export class UpdateMailTemplateDto extends PartialType(CreateMailTemplateDto) {}

export class GetMailsTemplateDto {
  @IsString()
  @IsOptional()
  skip?: string;

  @IsString()
  @IsOptional()
  limit?: string;

  @IsString()
  @IsOptional()
  sender?: string;

  @IsString()
  @IsOptional()
  senderName?: string;

  @IsString()
  @IsOptional()
  brand?: string;
}

export class GetMailTemplatesResponse {
  @Expose()
  @Type(() => MailTemplate)
  data: MailTemplate[];

  @Expose()
  total: number;

  @Expose()
  skip: number;

  @Expose()
  limit: number;
}
