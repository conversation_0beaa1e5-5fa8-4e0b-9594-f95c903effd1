import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  MailTemplate,
  MailTemplateDocument,
} from 'src/shared/entities/camp/mail-template.entity';
import { Model } from 'mongoose';
import {
  CreateMailTemplateDto,
  GetMailsTemplateDto,
  GetMailTemplatesResponse,
  UpdateMailTemplateDto,
} from './dto/mail-template.dto';
import { User } from 'src/shared/entities/auth/user.entity';
import { AdminDB } from 'src/config';
import { extractSubdomain } from 'src/shared/utils/common.util';
import { SYSTEM } from 'src/shared/constants/common.constant';

@Injectable()
export class MailTemplateService {
  constructor(
    @InjectModel(MailTemplate.name, AdminDB)
    private readonly mailTemplateModel: Model<MailTemplateDocument>,
  ) {}

  async create(
    createDto: CreateMailTemplateDto,
    authUser?: User,
  ): Promise<MailTemplate> {
    const now = new Date();
    return this.mailTemplateModel.create({
      ...createDto,
      brand: extractSubdomain(createDto.brand),
      createdAt: now,
      createdBy: authUser ? authUser.telegramId : SYSTEM,
      createdByUsername: authUser ? authUser.username : 'system',
    });
  }

  async find(
    getMailsTemplateDto: GetMailsTemplateDto,
  ): Promise<GetMailTemplatesResponse> {
    const {
      skip = 0,
      limit = 10,
      sender,
      senderName,
      brand,
    } = getMailsTemplateDto;

    const query: Record<string, any> = {};
    if (sender) {
      query.sender = {
        $regex: new RegExp(sender, 'i'),
      };
    }
    if (senderName) {
      query.senderName = {
        $regex: new RegExp(senderName, 'i'),
      };
    }
    if (brand) {
      query.brand = {
        $regex: new RegExp(extractSubdomain(brand), 'i'),
      };
    }

    const [total, data] = await Promise.all([
      this.mailTemplateModel.countDocuments(query),
      this.mailTemplateModel
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 }),
    ]);

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async findOne(id: string): Promise<MailTemplate> {
    const mailTemplate = await this.mailTemplateModel.findById(id);

    if (!mailTemplate) {
      throw new BadRequestException('Mail template not found');
    }

    return mailTemplate;
  }

  async update(
    id: string,
    updateDto: UpdateMailTemplateDto,
    authUser: User,
  ): Promise<MailTemplate> {
    const mailTemplate = await this.mailTemplateModel.findByIdAndUpdate(
      id,
      {
        ...updateDto,
        brand: updateDto.brand ? extractSubdomain(updateDto.brand) : undefined,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      },
      { new: true },
    );
    if (!mailTemplate) {
      throw new BadRequestException('Mail template not found');
    }
    return mailTemplate;
  }

  async remove(id: string, authUser: User): Promise<MailTemplate> {
    const mailTemplate = await this.mailTemplateModel.findByIdAndUpdate(
      id,
      {
        deletedAt: new Date(),
        deletedBy: authUser.telegramId,
      },
      { new: true },
    );
    if (!mailTemplate) {
      throw new BadRequestException('Mail template not found');
    }
    return mailTemplate;
  }
}
