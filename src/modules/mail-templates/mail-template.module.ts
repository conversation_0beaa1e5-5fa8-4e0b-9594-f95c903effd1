import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  MailTemplate,
  MailTemplateSchema,
} from 'src/shared/entities/camp/mail-template.entity';
import { AuthModule } from '../auth/auth.module';
import { ClientMailTemplateController } from './client-mail-template.controller';
import { MailTemplateController } from './mail-template.controller';
import { MailTemplateService } from './mail-template.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: MailTemplate.name,
          schema: MailTemplateSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [MailTemplateController, ClientMailTemplateController],
  providers: [MailTemplateService],
  exports: [MailTemplateService],
})
export class MailTemplateModule {}
