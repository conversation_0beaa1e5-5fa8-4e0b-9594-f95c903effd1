import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { MailTemplate } from 'src/shared/entities/camp/mail-template.entity';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import {
  CreateMailTemplateDto,
  GetMailsTemplateDto,
  GetMailTemplatesResponse,
} from './dto/mail-template.dto';
import { MailTemplateService } from './mail-template.service';

@ApiTags('Client Mail Templates')
@Controller('client/mail-templates')
@UseGuards(GetEmailGuard)
export class ClientMailTemplateController {
  constructor(private readonly mailTemplateService: MailTemplateService) {}

  @Get()
  async getMailTemplates(
    @Query() query: GetMailsTemplateDto,
  ): Promise<GetMailTemplatesResponse> {
    return this.mailTemplateService.find(query);
  }

  @Get(':id')
  async getMailTemplate(@Param('id') id: string): Promise<MailTemplate> {
    return this.mailTemplateService.findOne(id);
  }

  @Post()
  async createMailTemplate(
    @Body() createDto: CreateMailTemplateDto,
  ): Promise<MailTemplate> {
    return this.mailTemplateService.create(createDto);
  }
}
