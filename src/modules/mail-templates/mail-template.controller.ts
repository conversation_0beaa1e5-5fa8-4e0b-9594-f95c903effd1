import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Body,
  Patch,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { MailTemplateService } from './mail-template.service';
import {
  CreateMailTemplateDto,
  GetMailsTemplateDto,
  GetMailTemplatesResponse,
  UpdateMailTemplateDto,
} from './dto/mail-template.dto';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { MailTemplate } from 'src/shared/entities/camp/mail-template.entity';

@ApiTags('Mail Templates')
@ApiBearerAuth('JWT-auth')
@Controller('mail-templates')
@UseGuards(AuthGuard)
export class MailTemplateController {
  constructor(private readonly mailTemplateService: MailTemplateService) {}

  @Get()
  @ApiOperation({
    summary: 'Get mail templates',
    description:
      'Retrieve a paginated list of mail templates with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Mail templates retrieved successfully',
    type: GetMailTemplatesResponse,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getMailTemplates(
    @Query() query: GetMailsTemplateDto,
  ): Promise<GetMailTemplatesResponse> {
    return this.mailTemplateService.find(query);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get mail template detail',
    description: 'Retrieve detailed information about a specific mail template',
  })
  @ApiParam({
    name: 'id',
    description: 'Mail template ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Mail template retrieved successfully',
    type: MailTemplate,
  })
  @ApiResponse({
    status: 400,
    description: 'Mail template not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getMailTemplate(@Param('id') id: string): Promise<MailTemplate> {
    return this.mailTemplateService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: 'Create mail template',
    description: 'Create a new mail template',
  })
  @ApiBody({
    description: 'Mail template data',
    type: CreateMailTemplateDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Mail template created successfully',
    type: MailTemplate,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async createMailTemplate(
    @Body() createDto: CreateMailTemplateDto,
    @RequestUser() user: User,
  ): Promise<MailTemplate> {
    return this.mailTemplateService.create(createDto, user);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update mail template',
    description: 'Update an existing mail template',
  })
  @ApiParam({
    name: 'id',
    description: 'Mail template ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiBody({
    description: 'Mail template update data',
    type: UpdateMailTemplateDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Mail template updated successfully',
    type: MailTemplate,
  })
  @ApiResponse({
    status: 400,
    description: 'Mail template not found or invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async updateMailTemplate(
    @Param('id') id: string,
    @Body() updateDto: UpdateMailTemplateDto,
    @RequestUser() user: User,
  ): Promise<MailTemplate> {
    return this.mailTemplateService.update(id, updateDto, user);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete mail template',
    description: 'Soft delete a mail template (sets deletedAt timestamp)',
  })
  @ApiParam({
    name: 'id',
    description: 'Mail template ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Mail template deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Mail template not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteMailTemplate(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{ success: boolean }> {
    await this.mailTemplateService.remove(id, user);
    return { success: true };
  }
}
