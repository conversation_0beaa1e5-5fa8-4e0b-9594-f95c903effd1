import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import { Token, TokenSchema } from 'src/shared/entities/auth/token.entity';
import { User, UserSchema } from 'src/shared/entities/auth/user.entity';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { NotificationService } from 'src/shared/services/telegram.service';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import { Group, GroupSchema } from 'src/shared/entities/auth/group.entity';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Token.name,
          schema: TokenSchema,
        },
        {
          name: User.name,
          schema: UserSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: Group.name,
          schema: GroupSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
  ],
  controllers: [AuthController],
  providers: [AuthService, NotificationService],
  exports: [AuthService],
})
export class AuthModule {}
