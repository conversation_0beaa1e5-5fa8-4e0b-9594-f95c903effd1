import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { Model, Types } from 'mongoose';
import { Token, TokenDocument } from 'src/shared/entities/auth/token.entity';
import { User, UserDocument } from 'src/shared/entities/auth/user.entity';
import isNil from 'lodash/isNil';
import { LoginResponseDTO, UserResponseDTO } from './dto/login.dto';
import { AdminDB } from 'src/config';
import {
  ValidToken,
  ValidTokenDocument,
} from 'src/shared/entities/auth/valid-token.entity';
import { pick } from 'lodash';
import { plainToClass } from 'class-transformer';
import { AddUserDTO } from './dto/add-user.dto';
import { x54Api } from 'src/shared/utils/axios.util';
import { NotificationService } from 'src/shared/services/telegram.service';
import {
  AddUsersToGroupDTO,
  CreateGroupDTO,
  GetGroupResponseDTO,
  GetGroupsDTO,
  GetGroupsResponseDTO,
  RemoveUserFromGroupDTO,
  UpdateGroupDTO,
} from './dto/group.dto';
import { Group, GroupDocument } from 'src/shared/entities/auth/group.entity';
import {
  GroupUserConnection,
  GroupUserConnectionDocument,
} from 'src/shared/entities/auth/group-user-connection.entity';

@Injectable()
export class AuthService {
  constructor(
    private readonly configService: ConfigService,

    @InjectModel(Token.name, AdminDB)
    private tokenModel: Model<TokenDocument>,
    @InjectModel(User.name, AdminDB)
    private userModel: Model<UserDocument>,
    @InjectModel(ValidToken.name, AdminDB)
    private validTokenModel: Model<ValidTokenDocument>,
    @InjectModel(Group.name, AdminDB)
    private groupModel: Model<GroupDocument>,
    @InjectModel(GroupUserConnection.name, AdminDB)
    private groupUserConnectionModel: Model<GroupUserConnectionDocument>,
    private readonly notificationService: NotificationService,
  ) {}

  async loginWithAccount(username: string, password: string): Promise<string> {
    const hashPassword = await this.hashPassword(password);
    const user = await this.userModel.findOne({
      username,
      password: hashPassword,
    });
    if (isNil(user)) {
      throw new BadRequestException('Invalid username or password');
    }

    return this.generateTempToken(
      pick(user, [
        'telegramId',
        'firstName',
        'lastName',
        'username',
        'languageCode',
        'role',
      ]),
    );
  }

  async login(token: string): Promise<LoginResponseDTO> {
    const tokenExisted = await this.tokenModel.findOne({
      token,
      expiresAt: { $gt: new Date() },
    });
    if (isNil(tokenExisted)) {
      throw new BadRequestException('Invalid token');
    }

    const user = await this.userModel.findOne({
      telegramId: tokenExisted.telegramId,
    });
    if (isNil(user)) {
      throw new BadRequestException('User not found');
    }

    const { accessToken, refreshToken } = await this.generateToken(
      pick(user, [
        'telegramId',
        'firstName',
        'lastName',
        'username',
        'languageCode',
        'role',
      ]),
    );

    return {
      data: plainToClass(UserResponseDTO, user, {
        excludeExtraneousValues: true,
      }),
      auth: {
        accessToken,
        refreshToken,
      },
    };
  }

  async generateToken(payload: UserResponseDTO): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const accessToken = await this.generateAccessToken(payload);
    const refreshToken = await this.generateRefreshToken(payload);

    await this.validTokenModel.create({
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  async generateTempToken(payload: UserResponseDTO): Promise<string> {
    const userExisted = await this.userModel.findOne({
      telegramId: payload.telegramId,
    });

    if (isNil(userExisted)) {
      await this.userModel.create(payload);
      const telegramUsername = this.escapeMarkdown(
        payload.username || 'unknown',
      );
      await this.notificationService.sendNotificationToGroup(
        `User **${telegramUsername}** has created.`,
      );
    }

    const hash = crypto
      .createHash('sha256')
      .update(JSON.stringify({ ...payload, timestamp: Date.now() }))
      .digest('hex');
    const token = hash.substring(0, 16); // Return the first 16 characters

    await this.tokenModel.create({
      telegramId: payload.telegramId,
      userName: payload.username,
      token,
      expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 5 minutes
    });

    return token;
  }

  async generateAccessToken(payload: UserResponseDTO): Promise<string> {
    const jwtSecret = this.configService.get<string>('JWT_SECRET', '');
    const expiresIn = this.configService.get<string>(
      'JWT_ACCESS_EXPIRES_IN',
      '',
    );

    return jwt.sign(payload, jwtSecret, { expiresIn });
  }

  async generateRefreshToken(payload: UserResponseDTO): Promise<string> {
    const jwtSecretRefresh = this.configService.get<string>(
      'JWT_SECRET_REFRESH',
      '',
    );
    const expiresInRefresh = this.configService.get<string>(
      'JWT_REFRESH_EXPIRES_IN',
      '',
    );

    return jwt.sign(payload, jwtSecretRefresh, {
      expiresIn: expiresInRefresh,
    });
  }

  async verifyToken(
    token: string,
    secret: string,
  ): Promise<string | jwt.JwtPayload | null> {
    try {
      return jwt.verify(token, secret);
    } catch (err) {
      console.error('Invalid refresh token:', err.message);
      return null;
    }
  }

  async refreshToken(token: string): Promise<{
    accessToken: string;
    refreshToken: string;
  }> {
    const validRefreshToken = await this.validTokenModel.findOne({
      refreshToken: token,
      expiresAt: { $gt: new Date() },
    });
    if (isNil(validRefreshToken)) {
      throw new BadRequestException('Invalid refresh token');
    }
    await this.validTokenModel.deleteOne({ refreshToken: token });

    const jwtSecretRefresh = this.configService.get<string>(
      'JWT_SECRET_REFRESH',
      '',
    );

    const payload = await this.verifyToken(token, jwtSecretRefresh);
    if (!payload) {
      throw new BadRequestException('Invalid refresh token');
    }

    const user = {
      telegramId: payload['telegramId'],
      firstName: payload['firstName'],
      lastName: payload['lastName'],
      username: payload['username'],
      languageCode: payload['languageCode'],
      role: payload['role'],
    };

    const accessToken = await this.generateAccessToken(user);
    const refreshToken = await this.generateRefreshToken(user);

    await this.validTokenModel.create({
      accessToken,
      refreshToken,
      expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  async hashPassword(password: string): Promise<string> {
    const salt = this.configService.get<string>('PASSWORD_SALT', '');
    return crypto
      .pbkdf2Sync(password, salt, 1000, 64, 'sha512')
      .toString('hex');
  }

  async addUser(addUserDTO: AddUserDTO): Promise<UserResponseDTO> {
    const hashPassword = await this.hashPassword(addUserDTO.password);

    const userExisted = await this.userModel.findOne({
      username: addUserDTO.username,
    });
    if (!isNil(userExisted)) {
      throw new BadRequestException('Username already exists');
    }

    const user = await this.userModel.create({
      ...addUserDTO,
      password: hashPassword,
    });

    return plainToClass(UserResponseDTO, user, {
      excludeExtraneousValues: true,
    });
  }

  async checkUserWhitelist(username: string): Promise<boolean> {
    try {
      const response = await x54Api().post('/user', {
        username,
      });

      if (response.data && response.data.status) {
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking user whitelist:', error);
      throw new BadRequestException('Invalid username');
    }
  }

  async createGroup(
    createGroupDTO: CreateGroupDTO,
    authUser: User,
  ): Promise<GetGroupResponseDTO> {
    try {
      const groupExisted = await this.groupModel.findOne({
        name: createGroupDTO.name,
      });
      if (!isNil(groupExisted)) {
        throw new BadRequestException('Group already exists');
      }

      const group = await this.groupModel.create({
        ...createGroupDTO,
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        createdAt: new Date(),
      });

      return plainToClass(GetGroupResponseDTO, group, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.error('Error creating group:', error);
      throw new BadRequestException('Error creating group', error.message);
    }
  }

  async getGroups(getGroupsDTO: GetGroupsDTO): Promise<GetGroupsResponseDTO> {
    const { name, skip = 0, limit = 10 } = getGroupsDTO;

    const skipNumber = Number(skip);
    const limitNumber = Number(limit);

    const query: any = {};
    if (name) {
      query.name = { $regex: new RegExp(name, 'i') };
    }

    const [groups, total] = await Promise.all([
      this.groupModel.aggregate([
        { $match: query },
        { $sort: { createdAt: -1 } },
        { $skip: skipNumber },
        { $limit: limitNumber },
        {
          $lookup: {
            from: 'group_user_connections',
            localField: '_id',
            foreignField: 'groupId',
            as: 'connections',
          },
        },
        {
          $lookup: {
            from: 'users',
            localField: 'connections.userId',
            foreignField: '_id',
            as: 'users',
          },
        },
        {
          $project: {
            name: 1,
            description: 1,
            createdAt: 1,
            createdBy: 1,
            updatedAt: 1,
            updatedBy: 1,
            users: { _id: 1, username: 1 },
          },
        },
      ]),
      this.groupModel.countDocuments(query),
    ]);

    const data = await Promise.all(
      groups.map(async (group) => {
        return plainToClass(GetGroupResponseDTO, group, {
          excludeExtraneousValues: true,
        });
      }),
    );

    return {
      data,
      total,
      skip: skipNumber,
      limit: limitNumber,
    };
  }

  async updateGroup(
    id: string,
    updateGroupDTO: UpdateGroupDTO,
    authUser: User,
  ): Promise<GetGroupResponseDTO> {
    const group = await this.groupModel.findById(id);
    if (!group) {
      throw new BadRequestException('Group not found');
    }

    const updatedGroup = await this.groupModel.findByIdAndUpdate(
      id,
      {
        ...updateGroupDTO,
        updatedBy: authUser.telegramId,
        updatedAt: new Date(),
      },
      { new: true },
    );

    return plainToClass(GetGroupResponseDTO, updatedGroup, {
      excludeExtraneousValues: true,
    });
  }

  async deleteGroup(id: string): Promise<void> {
    const group = await this.groupModel.findById(id);
    if (!group) {
      throw new BadRequestException('Group not found');
    }
    await this.groupModel.findByIdAndDelete(id);
  }

  async addUserToGroup(addUsersToGroupDTO: AddUsersToGroupDTO): Promise<void> {
    const { groupId, userIds } = addUsersToGroupDTO;

    const group = await this.groupModel.findById(groupId);
    if (!group) {
      throw new BadRequestException('Group not found');
    }

    const users = await this.userModel.find({ _id: { $in: userIds } });
    if (users.length !== userIds.length) {
      throw new BadRequestException('Some users not found');
    }

    // Rule mỗi user chỉ có thể thuộc về 1 group
    const existingConnections = await this.groupUserConnectionModel.find({
      userId: { $in: userIds },
      groupId: { $ne: group._id },
    });
    if (existingConnections.length > 0) {
      throw new BadRequestException('Some users are already in another group');
    }

    await this.groupUserConnectionModel.insertMany(
      users.map((user) => ({
        groupId: group._id,
        userId: user._id,
      })),
    );
  }

  async removeUserFromGroup(
    removeUserFromGroupDTO: RemoveUserFromGroupDTO,
  ): Promise<void> {
    const { groupId, userId } = removeUserFromGroupDTO;

    const group = await this.groupModel.findById(groupId);
    if (!group) {
      throw new BadRequestException('Group not found');
    }
    const user = await this.userModel.findById(userId);
    if (!user) {
      throw new BadRequestException('User not found');
    }
    await this.groupUserConnectionModel.deleteOne({
      groupId: new Types.ObjectId(groupId),
      userId: new Types.ObjectId(userId),
    });
  }

  // Hàm tiện ích để thoát ký tự đặc biệt trong Markdown
  private escapeMarkdown(text: string): string {
    if (!text) return '';
    return text.replace(/([_*[\]()~`>#+\-=|{}.!])/g, '\\$1');
  }
}
