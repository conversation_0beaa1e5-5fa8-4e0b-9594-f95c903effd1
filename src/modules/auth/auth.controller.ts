import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ROLE } from 'src/shared/constants/auth.constant';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { Roles } from 'src/shared/decorators/role.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AddUserGuard } from 'src/shared/guards/add-user.guard';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { RolesGuard } from 'src/shared/guards/role.guard';
import { AuthService } from './auth.service';
import { AddUserDTO } from './dto/add-user.dto';
import {
  AddUsersToGroupDTO,
  CreateGroupDTO,
  GetGroupResponseDTO,
  GetGroupsDTO,
  GetGroupsResponseDTO,
  RemoveUserFromGroupDTO,
  UpdateGroupDTO,
} from './dto/group.dto';
import { LoginDTO, LoginResponseDTO, UserResponseDTO } from './dto/login.dto';
import { RefreshTokenDTO } from './dto/refresh-token.dto';

@ApiTags('Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({
    summary: 'User login',
    description:
      'Authenticate user with token and return access/refresh tokens',
  })
  @ApiBody({
    description: 'Login credentials',
    type: LoginDTO,
  })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: LoginResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid credentials',
  })
  async login(@Body() body: LoginDTO): Promise<LoginResponseDTO> {
    return await this.authService.login(body.token);
  }

  @Post('refresh-token')
  @ApiOperation({
    summary: 'Refresh access token',
    description: 'Generate new access token using refresh token',
  })
  @ApiBody({
    description: 'Refresh token',
    type: RefreshTokenDTO,
  })
  @ApiResponse({
    status: 200,
    description: 'Token refreshed successfully',
    schema: {
      type: 'object',
      properties: {
        accessToken: {
          type: 'string',
          example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid refresh token',
  })
  async refreshToken(@Body() refreshTokenDTO: RefreshTokenDTO): Promise<{
    accessToken: string;
  }> {
    return this.authService.refreshToken(refreshTokenDTO.refreshToken);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('me')
  @ApiOperation({
    summary: 'Get current user',
    description: 'Get current authenticated user information',
  })
  @ApiResponse({
    status: 200,
    description: 'User information retrieved successfully',
    type: User,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async me(@RequestUser() user: User): Promise<User> {
    return user;
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard, AddUserGuard)
  @Roles(ROLE.SUPER_ADMIN)
  @Post('add-user')
  @ApiOperation({
    summary: 'Add new user',
    description: 'Add a new user to the system (Super Admin only)',
  })
  @ApiBody({
    description: 'User data',
    type: AddUserDTO,
  })
  @ApiResponse({
    status: 201,
    description: 'User added successfully',
    type: UserResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid user data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Super Admin role required',
  })
  async addUser(@Body() body: AddUserDTO): Promise<UserResponseDTO> {
    return this.authService.addUser(body);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Get('groups')
  async getGroups(@Query() query: GetGroupsDTO): Promise<GetGroupsResponseDTO> {
    return this.authService.getGroups(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Patch(':id/update-group')
  async updateGroup(
    @Param('id') id: string,
    @RequestUser() user: User,
    @Body() body: UpdateGroupDTO,
  ): Promise<GetGroupResponseDTO> {
    return this.authService.updateGroup(id, body, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Post('create-group')
  async createGroup(
    @RequestUser() user: User,
    @Body() body: CreateGroupDTO,
  ): Promise<GetGroupResponseDTO> {
    return this.authService.createGroup(body, user);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Delete(':id/delete-group')
  async deleteGroup(@Param('id') id: string): Promise<{
    success: boolean;
  }> {
    await this.authService.deleteGroup(id);

    return {
      success: true,
    };
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Post('add-user-to-group')
  async addUsersToGroup(
    @Param('id') id: string,
    @Body() body: AddUsersToGroupDTO,
  ): Promise<{
    success: boolean;
  }> {
    await this.authService.addUserToGroup(body);

    return {
      success: true,
    };
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.SUPER_ADMIN, ROLE.ADMIN, ROLE.MANAGER)
  @Post('remove-user-from-group')
  async removeUserFromGroup(@Body() body: RemoveUserFromGroupDTO): Promise<{
    success: boolean;
  }> {
    await this.authService.removeUserFromGroup(body);

    return {
      success: true,
    };
  }
}
