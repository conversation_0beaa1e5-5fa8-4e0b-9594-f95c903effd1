import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';
import { User } from 'src/shared/entities/auth/user.entity';
import { ROLE } from 'src/shared/constants/auth.constant';

export class LoginDTO {
  @ApiProperty({
    description: 'Authentication token from Telegram',
    example: 'telegram_auth_token_here',
  })
  @IsString()
  @IsNotEmpty()
  token: string;
}

class Auth {
  @ApiProperty({
    description: 'JWT access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @Expose()
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @Expose()
  refreshToken: string;
}

export class UserResponseDTO {
  @ApiProperty({
    description: 'Telegram user ID',
    example: 123456789,
  })
  @Expose()
  telegramId: number;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @Expose()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @Expose()
  lastName: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'User language code',
    example: 'en',
  })
  @Expose()
  languageCode: string;

  @ApiProperty({
    description: 'User role',
    enum: ROLE,
    example: ROLE.USER,
  })
  @Expose()
  role: ROLE;
}

export class LoginResponseDTO {
  @ApiProperty({
    description: 'User information',
    type: UserResponseDTO,
  })
  @Expose()
  data: UserResponseDTO;

  @ApiProperty({
    description: 'Authentication tokens',
    type: Auth,
  })
  @Expose()
  auth: Auth;
}
