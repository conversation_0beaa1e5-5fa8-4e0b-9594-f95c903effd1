import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsEnum } from 'class-validator';
import { ROLE } from 'src/shared/constants/auth.constant';

export class AddUserDTO {
  @ApiProperty({
    description: 'Telegram user ID',
    example: 123456789,
  })
  @IsNotEmpty()
  @IsNumber()
  telegramId: number;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @IsNotEmpty()
  @IsString()
  username: string;

  @ApiProperty({
    description: 'User password',
    example: 'securePassword123',
  })
  @IsNotEmpty()
  @IsString()
  password: string;

  @ApiProperty({
    description: 'User language code',
    example: 'en',
  })
  @IsNotEmpty()
  @IsString()
  languageCode: string;

  @ApiProperty({
    description: 'User role',
    enum: ROLE,
    example: ROLE.USER,
  })
  @IsNotEmpty()
  @IsEnum(ROLE)
  role: ROLE;
}
