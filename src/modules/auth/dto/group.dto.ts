import { Expose, Transform, Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateGroupDTO {
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateGroupDTO {
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class DeleteGroupDTO {
  @IsString()
  @IsNotEmpty()
  id: string;
}

export class GetGroupsDTO {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  skip?: string;

  @IsString()
  @IsOptional()
  limit?: string;
}

export class UserGroupDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  username: string;
}

export class GetGroupResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  name: string;

  @Expose()
  description: string;

  @Expose()
  @Type(() => UserGroupDTO)
  users?: UserGroupDTO[];

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt: Date;

  @Expose()
  updatedBy: number;
}

export class GetGroupsResponseDTO {
  @Expose()
  data: GetGroupResponseDTO[];

  @Expose()
  total: number;

  @Expose()
  skip: number;

  @Expose()
  limit: number;
}

export class AddUsersToGroupDTO {
  @IsString()
  @IsNotEmpty()
  groupId: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  userIds: string[];
}

export class RemoveUserFromGroupDTO {
  @IsString()
  @IsNotEmpty()
  groupId: string;

  @IsString()
  @IsNotEmpty()
  userId: string;
}
