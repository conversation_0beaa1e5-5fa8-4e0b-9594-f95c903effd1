import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import { Page, PageSchema } from 'src/shared/entities/page.entity';
import { AuthModule } from '../auth/auth.module';
import { PagesController } from './page.controller';
import { PagesService } from './page.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Page.name,
          schema: PageSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [PagesController],
  providers: [PagesService],
  exports: [PagesService],
})
export class PagesModule {}
