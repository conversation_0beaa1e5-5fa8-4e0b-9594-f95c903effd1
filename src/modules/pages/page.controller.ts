import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { ROLE } from 'src/shared/constants/auth.constant';
import { PAGE_STATUS } from 'src/shared/constants/page.constant';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { Roles } from 'src/shared/decorators/role.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { RolesGuard } from 'src/shared/guards/role.guard';
import {
  GetPagesQueryDTO,
  GetPagesResponseDTO,
  PageDetailResponseDTO,
  PageResponseDTO,
} from './dto/get-pages.dto';
import { CreatePageDTO, UpdatePageDTO } from './dto/page.dto';
import { PagesService } from './page.service';

@ApiTags('Pages')
@ApiBearerAuth('JWT-auth')
@UseGuards(AuthGuard)
@Controller('pages')
export class PagesController {
  constructor(private readonly pagesService: PagesService) {}

  @Post('create')
  async createPage(
    @Body() CreatePageDTO: CreatePageDTO,
    @RequestUser() user: User,
  ): Promise<PageResponseDTO> {
    return this.pagesService.createPage(CreatePageDTO, user);
  }

  @Patch(':pageId')
  async updatePage(
    @Param('pageId') pageId: string,
    @Body() updatePageDTO: UpdatePageDTO,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.pagesService.updatePage(pageId, updatePageDTO, user);
    return { success: true };
  }

  @Get('')
  async getPages(
    @Query() query: GetPagesQueryDTO,
    @RequestUser() user: User,
  ): Promise<GetPagesResponseDTO> {
    return this.pagesService.getPages(query, user);
  }

  @Get('detail/:pageId')
  async getDetailPage(
    @Param('pageId') pageId: string,
    @RequestUser() user: User,
  ): Promise<PageDetailResponseDTO> {
    return this.pagesService.getPageById(pageId, user);
  }

  @UseGuards(RolesGuard)
  @Roles(ROLE.ADMIN, ROLE.SUPER_ADMIN)
  @Patch(':pageId/update-status')
  async updateStatus(
    @Param('pageId') pageId: string,
    @Body() { status }: { status: PAGE_STATUS },
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.pagesService.updatePageStatus(pageId, status, user);
    return { success: true };
  }

  // @Delete(':pageId')
  // async deletePage(
  //   @Param('pageId') pageId: string,
  //   @RequestUser() user: User,
  // ): Promise<{
  //   success: boolean;
  // }> {
  //   await this.pagesService.deletePage(pageId, user);
  //   return { success: true };
  // }
}
