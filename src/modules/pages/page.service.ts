import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToClass } from 'class-transformer';
import { isEmpty, isNil } from 'lodash';
import { Types } from 'mongoose';
import { AdminDB } from 'src/config';
import { ROLE } from 'src/shared/constants/auth.constant';
import { PAGE_STATUS } from 'src/shared/constants/page.constant';
import { User } from 'src/shared/entities/auth/user.entity';
import { Page, PageDocument } from 'src/shared/entities/page.entity';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import {
  GetPagesQueryDTO,
  GetPagesResponseDTO,
  PageDetailResponseDTO,
  PageResponseDTO,
} from './dto/get-pages.dto';
import { CreatePageDTO, UpdatePageDTO } from './dto/page.dto';

@Injectable()
export class PagesService {
  constructor(
    @InjectModel(Page.name, AdminDB)
    private pageModel: AuthModel<PageDocument>,
  ) {}

  async createPage(
    createPageDTO: CreatePageDTO,
    authUser: User,
  ): Promise<PageResponseDTO> {
    try {
      const code = await this.generateUniqueCode();

      const page = await this.pageModel.insertOne({
        ...createPageDTO,
        code,
        createdBy: authUser.telegramId,
        createdByUsername: authUser.username,
        createdAt: new Date(),
      });

      return plainToClass(PageResponseDTO, page, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        'Failed to create applicant',
        error.message,
      );
    }
  }

  async updatePage(
    id: string,
    updatePageDTO: UpdatePageDTO,
    authUser: User,
  ): Promise<void> {
    try {
      const page = await this.pageModel.findOneAndUpdateWithAuth(
        {
          _id: new Types.ObjectId(id),
        },
        {
          ...updatePageDTO,
          updatedBy: authUser.telegramId,
          updatedAt: new Date(),
        },
        {},
        authUser,
      );
      if (isNil(page)) {
        throw new BadRequestException('Page not found');
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Failed to update page', error.message);
    }
  }

  async getPages(
    query: GetPagesQueryDTO,
    authUser: User,
  ): Promise<GetPagesResponseDTO> {
    const { skip = 0, limit = 10, search, createdBy, domainId, status } = query;
    const filter = {};

    if (search) {
      filter['title'] = {
        $regex: new RegExp(search, 'i'),
      };
    }
    if (createdBy) {
      filter['createdBy'] = createdBy;
    }
    if (domainId) {
      filter['domainId'] = domainId;
    }
    if (status) {
      filter['status'] = status;
    }

    const [data, total] = await Promise.all([
      this.pageModel
        .aggregateWithAuth(
          [
            { $match: filter },
            {
              $lookup: {
                from: 'domains',
                let: { domainId: { $toObjectId: '$domainId' } },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$_id', '$$domainId'] },
                    },
                  },
                ],
                as: 'domain',
              },
            },
            {
              $unwind: {
                path: '$domain',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'job_applicants',
                let: { localField: '$_id' },
                pipeline: [
                  {
                    $addFields: {
                      pageIdObject: { $toObjectId: '$pageId' },
                    },
                  },
                  {
                    $match: {
                      $expr: { $eq: ['$$localField', '$pageIdObject'] },
                    },
                  },
                ],
                as: 'applicants',
              },
            },
            {
              $lookup: {
                from: 'jobs',
                let: { localField: '$_id' },
                pipeline: [
                  {
                    $addFields: {
                      pageIdObject: { $toObjectId: '$pageId' },
                    },
                  },
                  {
                    $match: {
                      $expr: { $eq: ['$$localField', '$pageIdObject'] },
                    },
                  },
                ],
                as: 'jobs',
              },
            },
            {
              $addFields: {
                totalJobs: { $size: '$jobs' },
              },
            },
            {
              $sort: { createdAt: -1 },
            },
            { $skip: Number(skip) },
            { $limit: Number(limit) },
          ],
          authUser,
        )
        .exec(),
      this.pageModel.countDocumentsWithAuth(filter, authUser),
    ]);

    return {
      data: data.map((page) =>
        plainToClass(PageResponseDTO, page, { excludeExtraneousValues: true }),
      ),
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async getPageById(
    pageId: string,
    authUser: User,
  ): Promise<PageDetailResponseDTO> {
    const pageWithDetails = await this.pageModel
      .aggregateWithAuth(
        [
          {
            $match: {
              _id: new Types.ObjectId(pageId),
              status: PAGE_STATUS.ACTIVATED,
            },
          },
          {
            $lookup: {
              from: 'domains',
              let: { domainId: { $toObjectId: '$domainId' } }, // Convert domainId to ObjectId
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$_id', '$$domainId'] }, // Match _id in domains with domainId
                  },
                },
              ],
              as: 'domain',
            },
          },
          {
            $unwind: {
              path: '$domain',
              preserveNullAndEmptyArrays: true, // Allow null if no domain is found
            },
          },
          {
            $lookup: {
              from: 'job_applicants',
              let: { localField: '$_id' },
              pipeline: [
                {
                  $addFields: {
                    pageIdObject: { $toObjectId: '$pageId' },
                  },
                },
                {
                  $match: {
                    $expr: { $eq: ['$$localField', '$pageIdObject'] },
                  },
                },
              ],
              as: 'applicants',
            },
          },
          {
            $lookup: {
              from: 'jobs',
              let: { localField: '$_id' },
              pipeline: [
                {
                  $addFields: {
                    pageIdObject: { $toObjectId: '$pageId' },
                  },
                },
                {
                  $match: {
                    $expr: { $eq: ['$$localField', '$pageIdObject'] },
                  },
                },
              ],
              as: 'jobs',
            },
          },
        ],
        authUser,
      )
      .exec();

    if (isNil(pageWithDetails) || pageWithDetails.length === 0) {
      throw new BadRequestException('Page not found');
    }

    return plainToClass(PageDetailResponseDTO, pageWithDetails[0], {
      excludeExtraneousValues: true,
    });
  }

  async deletePage(pageId: string, authUser: User): Promise<void> {
    const page = await this.getPageById(pageId, authUser);
    if (isNil(page)) {
      throw new BadRequestException('Page not found');
    }

    if (
      authUser.role !== ROLE.ADMIN &&
      !isEmpty(page.applicants) &&
      !isEmpty(page.jobs)
    ) {
      throw new ForbiddenException(
        'Cannot delete page with applicants or jobs',
      );
    }

    try {
      await this.pageModel.updateOneWithAuth(
        {
          _id: new Types.ObjectId(pageId),
        },
        {
          deletedAt: new Date(),
          deletedBy: authUser.telegramId,
        },
        authUser,
      );
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error delete page', error.message);
    }
  }

  async updatePageStatus(
    id: string,
    status: PAGE_STATUS,
    authUser: User,
  ): Promise<void> {
    try {
      const page = await this.pageModel.findOneAndUpdate(
        {
          _id: new Types.ObjectId(id),
        },
        {
          status,
          updatedBy: authUser.telegramId,
          updatedAt: new Date(),
        },
      );
      if (isNil(page)) {
        throw new BadRequestException('Page not found');
      }
    } catch (error) {
      console.log(error);
      throw new BadRequestException('Failed to update page', error.message);
    }
  }

  /**
   * * Generate a unique code for the page
   * Length: 8
   */
  async generateUniqueCode(): Promise<string> {
    const length = 8;
    const characters =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * characters.length),
      );
    }

    const existingPage = await this.pageModel.findOne({ code: result });

    if (existingPage) {
      return this.generateUniqueCode(); // Recursively generate a new code if it already exists
    }

    return result;
  }
}
