import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsN<PERSON>ber,
  Min,
  Max,
  IsEnum,
} from 'class-validator';
import { ApplicantResponseDTO } from 'src/modules/applicants/dto/get-applicants.dto';
import { DomainResponseDTO } from 'src/modules/domains/dto/get-domains.dto';
import { JobResponseDTO } from 'src/modules/jobs/dto/get-jobs.dto';
import { PAGE_STATUS } from 'src/shared/constants/page.constant';

export class GetPagesQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Search pages by title or description',
    example: 'careers',
  })
  @IsString()
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by creator ID',
    example: '123456789',
  })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Filter by domain ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  domainId?: string;

  @ApiPropertyOptional({
    description: 'Filter by page status',
    enum: PAGE_STATUS,
    example: PAGE_STATUS.ACTIVATED,
  })
  @IsOptional()
  @IsEnum(PAGE_STATUS)
  status?: PAGE_STATUS;
}

export class PageResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  title: string;

  @Expose()
  description: string;

  @Expose()
  totalApplicants: number;

  @Expose()
  totalJobs: number;

  @Expose()
  status: PAGE_STATUS;

  @Expose()
  @Type(() => DomainResponseDTO)
  domain: DomainResponseDTO;

  @Expose()
  code: string;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}

export class GetPagesResponseDTO {
  @ApiProperty({
    description: 'Array of pages',
    type: [PageResponseDTO],
  })
  @Expose()
  data: Array<PageResponseDTO>;

  @ApiProperty({
    description: 'Total number of pages matching the query',
    example: 25,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}

export class PageDetailResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  title: string;

  @Expose()
  description: string;

  @Expose()
  status: PAGE_STATUS;

  @Expose()
  applicants: Array<ApplicantResponseDTO>;

  @Expose()
  jobs: Array<JobResponseDTO>;

  @Expose()
  @Type(() => DomainResponseDTO)
  domain: DomainResponseDTO;

  @Expose()
  code: string;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}
