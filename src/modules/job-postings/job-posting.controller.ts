import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import {
  CreateJobPostingDto,
  DownloadJobPostingsDto,
  GetJobPostingsQueryDto,
} from './dto/job-posting.dto';
import { JobPostingService } from './job-posting.service';

@ApiTags('Job Postings')
@ApiBearerAuth('JWT-auth')
@Controller('job-postings')
@UseGuards(AuthGuard)
export class JobPostingController {
  constructor(private readonly jobPostingService: JobPostingService) {}

  @Post()
  @ApiOperation({
    summary: 'Create job posting',
    description: 'Create a new job posting with optional file upload',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Job posting data with optional file',
    schema: {
      type: 'object',
      properties: {
        brand: {
          type: 'string',
          description: 'Brand name',
          example: 'example.com',
        },
        job: {
          type: 'string',
          description: 'Job title',
          example: 'Software Engineer',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'Optional file attachment',
        },
      },
      required: ['brand', 'job'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Job posting created successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @Body() dto: CreateJobPostingDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.jobPostingService.create(dto, file);
  }

  @Get()
  @ApiOperation({
    summary: 'Get job postings',
    description:
      'Retrieve a paginated list of job postings with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Job postings retrieved successfully',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async findAll(@Query() query: GetJobPostingsQueryDto) {
    return this.jobPostingService.findAll(query);
  }

  @Get(':id/detail')
  @ApiOperation({
    summary: 'Get job posting detail',
    description: 'Retrieve detailed information about a specific job posting',
  })
  @ApiParam({
    name: 'id',
    description: 'Job posting ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Job posting retrieved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Job posting not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async findOne(@Param('id') id: string) {
    return this.jobPostingService.findOne(id);
  }

  @Get('download')
  @ApiOperation({
    summary: 'Download job posting file',
    description: 'Download the file associated with a specific job posting',
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Job posting not found or file not available',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async downloadFile(
    @Query() query: DownloadJobPostingsDto,
    @Res() res: Response,
  ) {
    const { brand, job } = query;
    const { stream, contentType } = await this.jobPostingService.downloadFile(
      brand,
      job,
    );
    res.set({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${brand}-${job}"`,
    });
    stream.pipe(res);
  }
}
