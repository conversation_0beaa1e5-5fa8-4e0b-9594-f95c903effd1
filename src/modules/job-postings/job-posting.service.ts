import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  JobPosting,
  JobPostingDocument,
} from 'src/shared/entities/job-posting.entity';
import { Model } from 'mongoose';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import {
  CreateJobPostingDto,
  GetJobPostingsQueryDto,
  GetJobPostingsResponseDto,
  UpdateJobPostingDto,
} from './dto/job-posting.dto';
import { AdminDB } from 'src/config';
import { extractSubdomain } from 'src/shared/utils/common.util';

@Injectable()
export class JobPostingService {
  constructor(
    @InjectModel(JobPosting.name, AdminDB)
    private readonly jobPostingModel: Model<JobPostingDocument>,
    private readonly azureStorageService: AzureStorageService,
  ) {}

  async create(
    dto: CreateJobPostingDto,
    file?: Express.Multer.File,
  ): Promise<JobPosting> {
    if (!file) {
      throw new NotFoundException('File not found');
    }

    const JobPostingExist = await this.jobPostingModel.findOne({
      brand: extractSubdomain(dto.brand),
      job: dto.job,
    });

    if (JobPostingExist) {
      throw new NotFoundException('Job posting already exists');
    }

    const fileUrl = await this.azureStorageService.uploadFile(file);

    return this.jobPostingModel.create({
      ...dto,
      brand: extractSubdomain(dto.brand),
      file: fileUrl,
    });
  }

  async findAll(
    GetJobPostingsQueryDto: GetJobPostingsQueryDto,
  ): Promise<GetJobPostingsResponseDto> {
    const { skip = 0, limit = 10, brand, job } = GetJobPostingsQueryDto;
    const query: any = {};
    if (brand)
      query.brand = {
        $regex: new RegExp(extractSubdomain(brand), 'i'),
      };
    if (job)
      query.job = {
        $regex: new RegExp(job, 'i'),
      };
    const [postings, total] = await Promise.all([
      this.jobPostingModel
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 }),
      this.jobPostingModel.countDocuments(query),
    ]);
    return {
      skip: Number(skip),
      limit: Number(limit),
      total,
      data: postings,
    };
  }

  async findOne(id: string): Promise<JobPosting> {
    const job = await this.jobPostingModel.findById(id);
    if (!job) throw new NotFoundException('Job posting not found');
    return job;
  }

  async update(
    id: string,
    dto: UpdateJobPostingDto,
    file?: Express.Multer.File,
  ): Promise<JobPosting> {
    let fileUrl: string | undefined;
    if (file) {
      fileUrl = await this.azureStorageService.uploadFile(file);
    }
    const updateData: any = { ...dto };
    if (fileUrl) updateData.file = fileUrl;

    const job = await this.jobPostingModel.findByIdAndUpdate(
      id,
      {
        ...updateData,
        brand: dto.brand ? extractSubdomain(dto.brand) : undefined,
      },
      {
        new: true,
      },
    );
    if (!job) throw new NotFoundException('Job posting not found');
    return job;
  }

  async remove(id: string): Promise<void> {
    await this.jobPostingModel.findByIdAndDelete(id);
  }

  async downloadFile(brand: string, job: string) {
    const posting = await this.jobPostingModel
      .findOne({
        brand: extractSubdomain(brand),
        job,
      })
      .lean();
    if (!posting || !posting.file)
      throw new NotFoundException('File not found');
    return this.azureStorageService.downloadFile(posting.file);
  }
}
