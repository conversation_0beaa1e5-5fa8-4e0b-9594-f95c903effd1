import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  JobPosting,
  JobPostingSchema,
} from 'src/shared/entities/job-posting.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { AuthModule } from '../auth/auth.module';
import { ClientJobPostingController } from './client-job-posting.controller';
import { JobPostingController } from './job-posting.controller';
import { JobPostingService } from './job-posting.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: JobPosting.name,
          schema: JobPostingSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [JobPostingController, ClientJobPostingController],
  providers: [JobPostingService, AzureStorageService],
  exports: [JobPostingService],
})
export class JobPostingModule {}
