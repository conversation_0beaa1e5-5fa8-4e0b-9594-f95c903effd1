import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsNotEmpty,
  <PERSON>N<PERSON><PERSON>,
  Min,
  Max,
} from 'class-validator';
import { JobPosting } from 'src/shared/entities/job-posting.entity';

export class CreateJobPostingDto {
  @IsString()
  brand: string;

  @IsString()
  job: string;
}

export class UpdateJobPostingDto {
  @IsOptional()
  @IsString()
  brand?: string;

  @IsOptional()
  @IsString()
  job?: string;
}

export class GetJobPostingsQueryDto {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by brand name',
    example: 'mybrand.com',
  })
  @IsString()
  @IsOptional()
  brand?: string;

  @ApiPropertyOptional({
    description: 'Filter by job title',
    example: 'developer',
  })
  @IsString()
  @IsOptional()
  job?: string;
}

export class GetJobPostingsResponseDto {
  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;

  @ApiProperty({
    description: 'Total number of job postings',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Array of job postings',
    type: [JobPosting],
  })
  @Expose()
  data: JobPosting[];
}

export class DownloadJobPostingsDto {
  @IsString()
  @IsNotEmpty()
  brand: string;

  @IsString()
  @IsNotEmpty()
  job: string;
}
