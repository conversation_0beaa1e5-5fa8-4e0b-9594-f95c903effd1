import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Response } from 'express';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import {
  CreateJobPostingDto,
  GetJobPostingsQueryDto,
  UpdateJobPostingDto,
} from './dto/job-posting.dto';
import { JobPostingService } from './job-posting.service';

@Controller('client/job-postings')
@UseGuards(GetEmailGuard)
export class ClientJobPostingController {
  constructor(private readonly jobPostingService: JobPostingService) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @Body() dto: CreateJobPostingDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.jobPostingService.create(dto, file);
  }

  @Get()
  async findAll(@Query() query: GetJobPostingsQueryDto) {
    return this.jobPostingService.findAll(query);
  }

  @Get(':id/detail')
  async findOne(@Param('id') id: string) {
    return this.jobPostingService.findOne(id);
  }

  @Patch(':id')
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('id') id: string,
    @Body() dto: UpdateJobPostingDto,
    @UploadedFile() file?: Express.Multer.File,
  ) {
    return this.jobPostingService.update(id, dto, file);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    await this.jobPostingService.remove(id);
    return { success: true };
  }

  @Get('download')
  async downloadFile(
    @Query('brand') brand: string,
    @Query('job') job: string,
    @Res() res: Response,
  ) {
    const { stream, contentType } = await this.jobPostingService.downloadFile(
      brand,
      job,
    );
    res.set({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${brand}-${job}"`,
    });
    stream.pipe(res);
  }
}
