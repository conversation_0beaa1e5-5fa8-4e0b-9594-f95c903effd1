import { Is<PERSON><PERSON>, <PERSON>N<PERSON>Empty, <PERSON>Optional, IsString } from 'class-validator';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';

export class CheckStatusShortLinkDTO {
  @IsString()
  @IsNotEmpty()
  shortLinkId: string;

  @IsString()
  @IsNotEmpty()
  ip: string;

  @IsString()
  @IsOptional()
  countryCode?: string;
}

export class UpdateStatusShortLinkDTO {
  @IsString()
  @IsNotEmpty()
  shortLinkId: string;

  @IsEnum(SHORT_LINK_STATUS)
  @IsNotEmpty()
  shortLinkStatus: SHORT_LINK_STATUS;
}
