import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  FileEmail,
  FileEmailDocument,
} from 'src/shared/entities/file/file-email.entity';
import {
  MAX_COUNT_CHECK,
  SHORT_LINK_STATUS,
} from 'src/shared/constants/short-link.constant';
import { isNil } from 'lodash';

@Injectable()
export class ShortLinkService {
  constructor(
    @InjectModel(FileEmail.name, AdminDB)
    private readonly fileEmailModel: Model<FileEmailDocument>,
  ) {}

  async checkStatusShortLink(shortLinkId: string): Promise<{
    success: boolean;
    expirationLink?: string;
  }> {
    const fileEmail = await this.fileEmailModel.findOne({
      shortLinkId,
    });

    if (isNil(fileEmail)) {
      throw new BadRequestException('Short link not found');
    }

    if (fileEmail.countCheck >= MAX_COUNT_CHECK) {
      await this.fileEmailModel.updateOne(
        { shortLinkId },
        { shortLinkStatus: SHORT_LINK_STATUS.DEACTIVATED },
      );

      return {
        success: false,
        expirationLink: fileEmail.expirationLink,
      };
    }

    await this.fileEmailModel.updateOne(
      { shortLinkId },
      { $inc: { countCheck: 1 } },
    );

    return {
      success: SHORT_LINK_STATUS.ACTIVATED === fileEmail.shortLinkStatus,
      expirationLink: fileEmail.expirationLink,
    };
  }

  async updateStatusShortLink(
    shortLinkId: string,
    shortLinkStatus: SHORT_LINK_STATUS,
  ): Promise<{
    success: boolean;
  }> {
    await this.fileEmailModel.updateOne({ shortLinkId }, { shortLinkStatus });
    return {
      success: true,
    };
  }
}
