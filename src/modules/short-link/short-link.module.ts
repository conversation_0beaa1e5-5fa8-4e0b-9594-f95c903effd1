import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  FileEmail,
  FileEmailSchema,
} from 'src/shared/entities/file/file-email.entity';
import { ShortLinkController } from './short-link.controller';
import { ShortLinkService } from './short-link.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: FileEmail.name,
          schema: FileEmailSchema,
        },
      ],
      AdminDB,
    ),
  ],
  controllers: [ShortLinkController],
  providers: [ShortLinkService],
  exports: [ShortLinkService],
})
export class ShortLinkModule {}
