import { Body, Controller, Get, Patch, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CountryCodeGuard } from 'src/shared/guards/country-code.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import {
  CheckStatusShortLinkDTO,
  UpdateStatusShortLinkDTO,
} from './dto/short-link.dto';
import { ShortLinkService } from './short-link.service';

@ApiTags('Short Links')
@UseGuards(GetEmailGuard)
@Controller('short-link')
export class ShortLinkController {
  constructor(private readonly shortLinkService: ShortLinkService) {}

  @UseGuards(CountryCodeGuard)
  @Get('check-status')
  async checkStatusShortLink(@Query() query: CheckStatusShortLinkDTO) {
    return this.shortLinkService.checkStatusShortLink(query.shortLinkId);
  }

  @Patch('update-status')
  async updateStatusShortLink(@Body() body: UpdateStatusShortLinkDTO) {
    return this.shortLinkService.updateStatusShortLink(
      body.shortLinkId,
      body.shortLinkStatus,
    );
  }
}
