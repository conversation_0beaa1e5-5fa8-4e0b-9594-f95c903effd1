import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import { Camp, CampSchema } from 'src/shared/entities/camp/camp.entity';
import {
  CampUser,
  CampUserSchema,
} from 'src/shared/entities/camp/camp-user.entity';
import {
  MailTemplate,
  MailTemplateSchema,
} from 'src/shared/entities/camp/mail-template.entity';
import {
  Webhook,
  WebhookSchema,
} from 'src/shared/entities/webhook/webhook.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { AuthModule } from '../auth/auth.module';
import { CampController } from './camp.controller';
import { CampService } from './camp.service';
import { ClientCampController } from './client-camp.controller';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Camp.name,
          schema: CampSchema,
        },
        {
          name: CampUser.name,
          schema: CampUserSchema,
        },
        {
          name: MailTemplate.name,
          schema: MailTemplateSchema,
        },
        {
          name: Webhook.name,
          schema: WebhookSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [CampController, ClientCampController],
  providers: [CampService, AzureStorageService],
  exports: [CampService],
})
export class CampsModule {}
