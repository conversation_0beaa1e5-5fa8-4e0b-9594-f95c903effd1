import {
  Controller,
  Get,
  Post,
  Delete,
  Param,
  Query,
  Body,
  UploadedFile,
  UseInterceptors,
  UseGuards,
  Patch,
  Res,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { CampService } from './camp.service';
import { CreateCampDTO, GetCampQueryDTO, UpdateCampDTO } from './dto/camp.dto';
import {
  GetCampResponseDTO,
  GetCampsResponseDTO,
  CampaignOverviewResponseDTO,
  EmailPerformanceResponseDTO,
  UserSegmentAnalysisResponseDTO,
  TimeAnalysisResponseDTO,
  ErrorAnalysisResponseDTO,
} from './dto/camp.dto';
import {
  UpdateEmailTrackingDTO,
  CampUserResponseDTO,
} from './dto/camp-user.dto';
import { Response } from 'express';

@ApiTags('Camps')
@ApiBearerAuth('JWT-auth')
@Controller('camps')
@UseGuards(AuthGuard)
export class CampController {
  constructor(private readonly campService: CampService) {}

  @Get()
  @ApiOperation({
    summary: 'Get campaigns',
    description:
      'Retrieve a paginated list of campaigns with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaigns retrieved successfully',
    type: GetCampsResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getCamps(
    @Query() getCampsQueryDTO: GetCampQueryDTO,
  ): Promise<GetCampsResponseDTO> {
    return this.campService.getCamps(getCampsQueryDTO);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get campaign detail',
    description: 'Retrieve detailed information about a specific campaign',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign retrieved successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getCampDetail(@Param('id') id: string): Promise<GetCampResponseDTO> {
    return this.campService.getCampDetail(id);
  }

  @Post()
  @ApiOperation({
    summary: 'Create campaign',
    description: 'Create a new campaign with CSV file upload for user data',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Campaign data with CSV file',
    schema: {
      type: 'object',
      properties: {
        brand: {
          type: 'string',
          description: 'Brand name',
          example: 'example.com',
        },
        webhook: {
          type: 'string',
          description: 'Webhook ID for notifications',
          example: '507f1f77bcf86cd799439011',
        },
        startDate: {
          type: 'string',
          description: 'Campaign start date',
          example: '2024-01-01T00:00:00.000Z',
        },
        mail: {
          type: 'string',
          description: 'Mail template ID',
          example: '507f1f77bcf86cd799439012',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'CSV file with user data (EMAIL,COUNTRY,FULLNAME,JOB)',
        },
      },
      required: ['brand', 'webhook', 'startDate', 'mail', 'file'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Campaign created successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or file format',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.csv$/)) {
          return callback(
            new BadRequestException('Only .csv files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async createCamp(
    @Body() createCampDTO: CreateCampDTO,
    @UploadedFile() file: Express.Multer.File,
    @RequestUser() user: User,
  ): Promise<GetCampResponseDTO> {
    return this.campService.createCamp(createCampDTO, file, user);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update campaign',
    description: 'Update an existing campaign with optional CSV file upload',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Campaign update data with optional CSV file',
    schema: {
      type: 'object',
      properties: {
        brand: {
          type: 'string',
          description: 'Brand name',
          example: 'example.com',
        },
        webhook: {
          type: 'string',
          description: 'Webhook ID for notifications',
          example: '507f1f77bcf86cd799439011',
        },
        startDate: {
          type: 'string',
          description: 'Campaign start date',
          example: '2024-01-01T00:00:00.000Z',
        },
        mail: {
          type: 'string',
          description: 'Mail template ID',
          example: '507f1f77bcf86cd799439012',
        },
        file: {
          type: 'string',
          format: 'binary',
          description:
            'CSV file with user data (EMAIL,COUNTRY,FULLNAME,JOB) - optional',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign updated successfully',
    type: GetCampResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or invalid input data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (_req, file, callback) => {
        if (!file.originalname.match(/\.csv$/)) {
          return callback(
            new BadRequestException('Only .csv files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async updateCamp(
    @Param('id') id: string,
    @Body() updateCampDTO: UpdateCampDTO,
    @RequestUser() user: User,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<GetCampResponseDTO> {
    return this.campService.updateCamp(id, updateCampDTO, user, file);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete campaign',
    description: 'Soft delete a campaign (sets deletedAt timestamp)',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteCamp(
    @Param('id') id: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.campService.deleteCamp(id, user);

    return {
      success: true,
    };
  }

  @Get(':id/download')
  @ApiOperation({
    summary: 'Download campaign file',
    description: 'Download the CSV file associated with the campaign',
  })
  @ApiParam({
    name: 'id',
    description: 'Campaign ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found or file not available',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } = await this.campService.downloadFile(id);

    res.set({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="file"`,
    });

    stream.pipe(res);
  }

  @Get(':campaignId/overview')
  @ApiOperation({
    summary: 'Get campaign overview',
    description:
      'Returns overview information about a campaign based on camp_users table and mail template duration',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Campaign overview retrieved successfully',
    type: CampaignOverviewResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getCampaignOverview(
    @Param('campaignId') campaignId: string,
  ): Promise<CampaignOverviewResponseDTO> {
    return this.campService.getCampaignOverview(campaignId);
  }

  @Get(':campaignId/email-performance')
  @ApiOperation({
    summary: 'Get email performance statistics',
    description:
      'Returns performance metrics for each email type in the campaign',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Email performance statistics retrieved successfully',
    type: EmailPerformanceResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getEmailPerformance(
    @Param('campaignId') campaignId: string,
  ): Promise<EmailPerformanceResponseDTO> {
    return this.campService.getEmailPerformance(campaignId);
  }

  @Get(':campaignId/user-segments')
  @ApiOperation({
    summary: 'Get user segment analysis',
    description:
      'Returns campaign performance metrics grouped by user segments (position/job)',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'User segment analysis retrieved successfully',
    type: UserSegmentAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getUserSegmentAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<UserSegmentAnalysisResponseDTO> {
    return this.campService.getUserSegmentAnalysis(campaignId);
  }

  @Get(':campaignId/time-analysis')
  @ApiOperation({
    summary: 'Get time analysis statistics',
    description:
      'Returns time-based analytics including average time to open emails and conversion trends',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Time analysis statistics retrieved successfully',
    type: TimeAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getTimeAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<TimeAnalysisResponseDTO> {
    return this.campService.getTimeAnalysis(campaignId);
  }

  @Patch(':campaignId/users/:userId/email-tracking')
  @ApiOperation({
    summary: 'Update email tracking',
    description: 'Updates email tracking status for a user in the campaign',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID',
    example: 'user123',
  })
  @ApiBody({
    description: 'Email tracking update data',
    type: UpdateEmailTrackingDTO,
  })
  @ApiResponse({
    status: 200,
    description: 'Email tracking updated successfully',
    type: CampUserResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'User not found in campaign',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async updateEmailTracking(
    @Param('campaignId') campaignId: string,
    @Param('userId') userId: string,
    @Body() updateDto: UpdateEmailTrackingDTO,
  ): Promise<CampUserResponseDTO> {
    return this.campService.updateEmailTracking(userId, campaignId, updateDto);
  }

  @Get(':campaignId/errors')
  @ApiOperation({
    summary: 'Get error analysis statistics',
    description:
      'Returns error analysis including failed emails, bounce rate, and non-interactive users',
  })
  @ApiParam({
    name: 'campaignId',
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @ApiResponse({
    status: 200,
    description: 'Error analysis statistics retrieved successfully',
    type: ErrorAnalysisResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Campaign not found',
  })
  async getErrorAnalysis(
    @Param('campaignId') campaignId: string,
  ): Promise<ErrorAnalysisResponseDTO> {
    return this.campService.getErrorAnalysis(campaignId);
  }
}
