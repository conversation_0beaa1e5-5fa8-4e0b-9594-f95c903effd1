import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { plainToInstance } from 'class-transformer';
import { get } from 'lodash';
import csv from 'csv-parser';
import { Readable } from 'stream';
import { AdminDB } from 'src/config';
import { SYSTEM } from 'src/shared/constants/common.constant';
import { User } from 'src/shared/entities/auth/user.entity';
import { Camp, CampDocument } from 'src/shared/entities/camp/camp.entity';
import {
  CampUser,
  CampUserDocument,
} from 'src/shared/entities/camp/camp-user.entity';
import {
  EMAIL_STATUS,
  CAMP_USER_STATUS,
  EMAIL_TYPE,
} from 'src/shared/constants/camp.constant';
import {
  MailTemplate,
  MailTemplateDocument,
} from 'src/shared/entities/camp/mail-template.entity';
import {
  Webhook,
  WebhookDocument,
} from 'src/shared/entities/webhook/webhook.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { extractSubdomain } from 'src/shared/utils/common.util';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import {
  CreateCampDTO,
  GetCampQueryDTO,
  GetCampResponseDTO,
  GetCampsResponseDTO,
  UpdateCampDTO,
  CampaignOverviewResponseDTO,
  EmailPerformanceResponseDTO,
  EmailPerformanceMetricsDTO,
  UserSegmentAnalysisResponseDTO,
  TimeAnalysisResponseDTO,
  TrendDataDTO,
  ErrorAnalysisResponseDTO,
} from './dto/camp.dto';
import {
  UpdateEmailTrackingDTO,
  CampUserResponseDTO,
} from './dto/camp-user.dto';

@Injectable()
export class CampService {
  constructor(
    @InjectModel(Camp.name, AdminDB)
    private readonly campModel: AuthModel<CampDocument>,
    @InjectModel(CampUser.name, AdminDB)
    private readonly campUserModel: AuthModel<CampUserDocument>,
    @InjectModel(MailTemplate.name, AdminDB)
    private readonly mailTemplateModel: AuthModel<MailTemplateDocument>,
    @InjectModel(Webhook.name, AdminDB)
    private readonly webhookModel: AuthModel<WebhookDocument>,
    private readonly azureStorageService: AzureStorageService,
  ) {}

  async getCamps(
    getCampsQueryDTO: GetCampQueryDTO,
  ): Promise<GetCampsResponseDTO> {
    const { skip = 0, limit = 10, brand, startDate } = getCampsQueryDTO;

    const query: Record<string, any> = {};
    if (brand) {
      query.brand = extractSubdomain(brand);
    }
    if (startDate) {
      query.startDate = {
        $gte: new Date(startDate),
      };
    }

    const [camps, total] = await Promise.all([
      this.campModel
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 })
        .lean(),
      this.campModel.countDocuments(query),
    ]);

    const data = camps.map((camp) =>
      plainToInstance(GetCampResponseDTO, camp, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async getCampDetail(id: string): Promise<GetCampResponseDTO> {
    const camp = await this.campModel
      .findOne({ _id: id })
      .populate('mail')
      .populate('webhook')
      .lean()
      .exec();

    if (!camp) {
      throw new BadRequestException('Camp not found');
    }

    return plainToInstance(GetCampResponseDTO, camp, {
      excludeExtraneousValues: true,
    });
  }

  async createCamp(
    createCampDTO: CreateCampDTO,
    file: Express.Multer.File,
    authUser?: User,
  ): Promise<GetCampResponseDTO> {
    const { brand, webhook, startDate } = createCampDTO;

    if (!file) {
      throw new BadRequestException('File is required');
    }

    const webhookDoc = await this.webhookModel.findById(webhook).lean().exec();
    if (!webhookDoc) {
      throw new BadRequestException('Webhook not found');
    }

    const fileUrl = await this.azureStorageService.uploadFile(file);

    const newCamp = new this.campModel({
      brand: extractSubdomain(brand),
      webhook: new Types.ObjectId(webhook),
      startDate: new Date(startDate),
      file: fileUrl,
      mail: createCampDTO.mail,
      createdAt: new Date(),
      createdBy: authUser ? authUser.telegramId : SYSTEM,
      createdByUsername: authUser ? authUser.username : 'system',
    });
    const savedCamp = await newCamp.save();

    // Auto sync users from CSV file after creating campaign
    try {
      await this.syncUsersFromFile(
        (savedCamp._id as any).toString(),
        file,
        authUser || ({ telegramId: SYSTEM } as User),
      );
    } catch (error) {
      console.error('Failed to sync users after creating campaign:', error);
    }

    return plainToInstance(GetCampResponseDTO, savedCamp, {
      excludeExtraneousValues: true,
    });
  }

  async updateCamp(
    id: string,
    updateCampDTO: UpdateCampDTO,
    authUser: User,
    file?: Express.Multer.File,
  ): Promise<GetCampResponseDTO> {
    const { webhook, brand, startDate, mail } = updateCampDTO;

    const camp = await this.campModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean();

    if (!camp) {
      throw new BadRequestException('Camp not found');
    }

    if (webhook) {
      const webhookDoc = await this.webhookModel
        .findById(webhook)
        .lean()
        .exec();
      if (!webhookDoc) {
        throw new BadRequestException('Webhook not found');
      }
    }

    let fileUrl = get(camp, 'file', '');
    if (file) {
      fileUrl = await this.azureStorageService.uploadFile(file);
    }

    const bodyUpdate: Partial<CampDocument> = {};

    if (brand) {
      bodyUpdate.brand = extractSubdomain(brand);
    }
    if (webhook) {
      bodyUpdate.webhook = new Types.ObjectId(webhook);
    }
    if (startDate) {
      bodyUpdate.startDate = new Date(startDate);
    }
    if (fileUrl) {
      bodyUpdate.file = fileUrl;
    }
    if (mail) {
      bodyUpdate.mail = mail;
    }

    const updatedCamp = await this.campModel.findOneAndUpdateWithAuth(
      {
        _id: id,
      },
      {
        ...bodyUpdate,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      },
      { new: true },
      authUser,
    );

    // Auto sync users from CSV file if new file is uploaded
    if (file) {
      try {
        await this.syncUsersFromFile(id, file, authUser);
      } catch (error) {
        console.error('Failed to sync users after updating campaign:', error);
      }
    }

    return plainToInstance(GetCampResponseDTO, updatedCamp, {
      excludeExtraneousValues: true,
    });
  }

  async deleteCamp(id: string, authUser: User): Promise<{ success: boolean }> {
    const camp = await this.campModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean()
      .exec();

    if (!camp) {
      throw new BadRequestException('Camp not found');
    }

    await this.campModel
      .findByIdAndUpdate(id, {
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
        deletedAt: new Date(),
        deletedBy: authUser.telegramId,
      })
      .lean()
      .exec();

    return { success: true };
  }

  async downloadFile(
    id: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const camp = await this.campModel.findById(id);
      if (!camp) {
        throw new BadRequestException('Camp not found');
      }

      return this.azureStorageService.downloadFile(camp.file);
    } catch (error) {
      throw new BadRequestException('Failed to download file', error.message);
    }
  }

  async getCampaignOverview(
    campaignId: string,
  ): Promise<CampaignOverviewResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const totalUsers = await this.campUserModel.countDocuments({
      campId: campaignId,
    });

    const emailStats = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: null,
          totalEmailsSent: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [
                      EMAIL_STATUS.SENT,
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                1,
                0,
              ],
            },
          },
          successfulUsers: {
            $addToSet: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                '$userId',
                null,
              ],
            },
          },
        },
      },
      {
        $project: {
          totalEmailsSent: 1,
          successfulUsersCount: {
            $size: {
              $filter: {
                input: '$successfulUsers',
                cond: { $ne: ['$$this', null] },
              },
            },
          },
        },
      },
    ]);

    const stats = emailStats[0] || {
      totalEmailsSent: 0,
      successfulUsersCount: 0,
    };
    const totalEmailsSent = stats.totalEmailsSent;
    const successfulUsers = stats.successfulUsersCount;

    const successRate =
      totalUsers > 0 ? (successfulUsers / totalUsers) * 100 : 0;

    let totalDuration = 0;
    if (camp.mail) {
      const mailTemplate = await this.mailTemplateModel
        .findById(camp.mail)
        .lean()
        .exec();
      if (mailTemplate) {
        totalDuration += mailTemplate.startHTML?.delay || 0;

        if (mailTemplate.otherHTML && mailTemplate.otherHTML.length > 0) {
          totalDuration += mailTemplate.otherHTML.reduce(
            (sum, html) => sum + (html.delay || 0),
            0,
          );
        }

        totalDuration += mailTemplate.endHTML?.delay || 0;
      }
    }

    const durationString = this.formatDuration(totalDuration);

    return plainToInstance(
      CampaignOverviewResponseDTO,
      {
        campaignId: camp._id.toString(),
        brand: camp.brand,
        startDate: camp.startDate.toISOString(),
        totalUsers,
        totalEmailsSent,
        successRate: Math.round(successRate * 10) / 10,
        duration: durationString,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  async updateEmailTracking(
    email: string,
    campId: string,
    updateDto: UpdateEmailTrackingDTO,
  ): Promise<CampUserResponseDTO> {
    const campUser = await this.campUserModel.findOne({ email, campId });
    if (!campUser) {
      throw new BadRequestException('User not found in campaign');
    }

    let emailTrack = campUser.emailTracking.find(
      (track) => track.emailType === updateDto.emailType,
    );

    if (!emailTrack) {
      emailTrack = {
        emailType: updateDto.emailType,
        status: updateDto.status,
        openCount: 0,
        clickCount: 0,
      };
      campUser.emailTracking.push(emailTrack);
    } else {
      emailTrack.status = updateDto.status;
    }

    const timestamp = updateDto.timestamp || new Date();
    switch (updateDto.status) {
      case EMAIL_STATUS.SENT:
        emailTrack.sentAt = timestamp;
        campUser.lastEmailSentAt = timestamp;
        break;
      case EMAIL_STATUS.DELIVERED:
        emailTrack.deliveredAt = timestamp;
        break;
      case EMAIL_STATUS.OPENED:
        emailTrack.openedAt = timestamp;
        emailTrack.openCount += 1;
        break;
      case EMAIL_STATUS.CLICKED:
        emailTrack.clickedAt = timestamp;
        emailTrack.clickCount += 1;
        break;
      case EMAIL_STATUS.BOUNCED:
        emailTrack.bouncedAt = timestamp;
        break;
      case EMAIL_STATUS.FAILED:
        emailTrack.failedAt = timestamp;
        break;
    }

    campUser.updatedAt = new Date();
    const updatedUser = await campUser.save();

    return plainToInstance(CampUserResponseDTO, updatedUser.toObject(), {
      excludeExtraneousValues: true,
    });
  }

  async getEmailPerformance(
    campaignId: string,
  ): Promise<EmailPerformanceResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const performanceData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: '$emailTracking.emailType',
          sent: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [
                      EMAIL_STATUS.SENT,
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                1,
                0,
              ],
            },
          },
          opened: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$emailTracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
                1,
                0,
              ],
            },
          },
          clicked: {
            $sum: {
              $cond: [
                { $eq: ['$emailTracking.status', EMAIL_STATUS.CLICKED] },
                1,
                0,
              ],
            },
          },
        },
      },
    ]);

    const conversionData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
        },
      },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.emailType': EMAIL_TYPE.END,
          'emailTracking.status': {
            $in: [
              EMAIL_STATUS.SENT,
              EMAIL_STATUS.DELIVERED,
              EMAIL_STATUS.OPENED,
              EMAIL_STATUS.CLICKED,
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          converted: { $sum: 1 },
        },
      },
    ]);

    const converted = conversionData[0]?.converted || 0;

    return this.buildEmailPerformanceResponse(performanceData, converted);
  }

  private buildEmailPerformanceResponse(
    performanceData: any[],
    converted: number,
  ): EmailPerformanceResponseDTO {
    const response: any = {};

    performanceData.forEach((data) => {
      if (!data._id) return;

      const emailType = data._id;
      const responseKey = this.getResponseKey(emailType);

      if (!responseKey) return;

      const sent = data.sent || 0;
      const opened = data.opened || 0;
      const clicked = data.clicked || 0;

      const metrics: EmailPerformanceMetricsDTO = {
        sent,
        opened,
        openRate: sent > 0 ? Math.round((opened / sent) * 1000) / 10 : 0,
        clicked,
        clickRate: sent > 0 ? Math.round((clicked / sent) * 1000) / 10 : 0,
      };

      if (emailType === EMAIL_TYPE.END) {
        metrics.converted = converted;
        metrics.conversionRate =
          sent > 0 ? Math.round((converted / sent) * 1000) / 10 : 0;
      }

      response[responseKey] = metrics;
    });

    if (!response.start) {
      response.start = {
        sent: 0,
        opened: 0,
        openRate: 0,
        clicked: 0,
        clickRate: 0,
      };
    }

    if (!response.end) {
      response.end = {
        sent: 0,
        opened: 0,
        openRate: 0,
        clicked: 0,
        clickRate: 0,
        converted: 0,
        conversionRate: 0,
      };
    }

    return response as EmailPerformanceResponseDTO;
  }

  getResponseKey = (emailType: string): string | null => {
    if (emailType === EMAIL_TYPE.START) return 'start';
    if (emailType === EMAIL_TYPE.END) return 'end';

    // Xử lý dynamic cho other emails: other_1 -> other[0], other_2 -> other[1], etc.
    if (emailType.startsWith('other_')) {
      const indexMatch = emailType.match(/other_(\d+)/);
      if (indexMatch) {
        const index = parseInt(indexMatch[1]) - 1; // other_1 -> index 0
        return `other[${index}]`;
      }
    }

    return null; // Unknown email type
  };

  async getUserSegmentAnalysis(
    campaignId: string,
  ): Promise<UserSegmentAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const segmentData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      {
        $addFields: {
          position: {
            $ifNull: ['$metadata.job', 'Other'],
          },
        },
      },
      {
        $group: {
          _id: '$position',
          totalUsers: { $sum: 1 },
          users: { $push: '$$ROOT' },
        },
      },
      {
        $unwind: {
          path: '$users',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$users.emailTracking',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: {
            position: '$_id',
            userId: '$users.userId',
          },
          totalUsers: { $first: '$totalUsers' },
          position: { $first: '$_id' },
          emailsSent: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$users.emailTracking.status',
                    [
                      EMAIL_STATUS.SENT,
                      EMAIL_STATUS.DELIVERED,
                      EMAIL_STATUS.OPENED,
                      EMAIL_STATUS.CLICKED,
                    ],
                  ],
                },
                1,
                0,
              ],
            },
          },
          emailsOpened: {
            $sum: {
              $cond: [
                {
                  $in: [
                    '$users.emailTracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
                1,
                0,
              ],
            },
          },
          emailsClicked: {
            $sum: {
              $cond: [
                { $eq: ['$users.emailTracking.status', EMAIL_STATUS.CLICKED] },
                1,
                0,
              ],
            },
          },
          isConverted: {
            $max: {
              $cond: [
                { $eq: ['$users.status', CAMP_USER_STATUS.COMPLETED] },
                1,
                0,
              ],
            },
          },
        },
      },
      {
        $group: {
          _id: '$position',
          totalUsers: { $first: '$totalUsers' },
          totalEmailsSent: { $sum: '$emailsSent' },
          totalEmailsOpened: { $sum: '$emailsOpened' },
          totalEmailsClicked: { $sum: '$emailsClicked' },
          totalConversions: { $sum: '$isConverted' },
        },
      },
      {
        $project: {
          _id: 0,
          position: '$_id',
          totalUsers: 1,
          openRate: {
            $cond: [
              { $gt: ['$totalEmailsSent', 0] },
              {
                $multiply: [
                  { $divide: ['$totalEmailsOpened', '$totalEmailsSent'] },
                  100,
                ],
              },
              0,
            ],
          },
          clickRate: {
            $cond: [
              { $gt: ['$totalEmailsSent', 0] },
              {
                $multiply: [
                  { $divide: ['$totalEmailsClicked', '$totalEmailsSent'] },
                  100,
                ],
              },
              0,
            ],
          },
          conversionRate: {
            $cond: [
              { $gt: ['$totalUsers', 0] },
              {
                $multiply: [
                  { $divide: ['$totalConversions', '$totalUsers'] },
                  100,
                ],
              },
              0,
            ],
          },
        },
      },
      {
        $sort: { totalUsers: -1 },
      },
    ]);

    const byPosition = segmentData.map((segment) => ({
      position: segment.position || 'Other',
      totalUsers: segment.totalUsers || 0,
      openRate: Math.round((segment.openRate || 0) * 10) / 10,
      clickRate: Math.round((segment.clickRate || 0) * 10) / 10,
      conversionRate: Math.round((segment.conversionRate || 0) * 10) / 10,
    }));

    return plainToInstance(
      UserSegmentAnalysisResponseDTO,
      {
        byPosition,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  async syncUsersFromFile(
    campaignId: string,
    file: Express.Multer.File,
    authUser: User,
  ): Promise<{ success: boolean; usersCount: number }> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const csvData = await this.parseCSVFile(file);

    this.validateCSVFormat(csvData);

    await this.campUserModel.deleteMany({ campId: campaignId });

    const campUsers = csvData.map((row, index) => {
      const [firstName, ...lastNameParts] = row.FULLNAME.trim().split(' ');
      const lastName = lastNameParts.join(' ') || '';

      return {
        userId: `${campaignId}_user_${index + 1}`,
        campId: campaignId,
        email: row.EMAIL.trim().toLowerCase(),
        firstName: firstName || '',
        lastName: lastName,
        status: CAMP_USER_STATUS.ACTIVE,
        enrolledAt: new Date(),
        emailTracking: this.createInitialEmailTracking(),
        metadata: {
          country: row.COUNTRY?.trim() || '',
          job: row.JOB?.trim() || '',
          originalIndex: index + 1,
        },
        createdAt: new Date(),
        createdBy: authUser.telegramId,
      };
    });

    if (campUsers.length > 0) {
      await this.campUserModel.insertMany(campUsers);
    }

    return {
      success: true,
      usersCount: campUsers.length,
    };
  }

  private async parseCSVFile(file: Express.Multer.File): Promise<any[]> {
    return new Promise((resolve, reject) => {
      const results: any[] = [];
      const stream = Readable.from(file.buffer);

      stream
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  private validateCSVFormat(csvData: any[]): void {
    if (!csvData || csvData.length === 0) {
      throw new BadRequestException('CSV file is empty or invalid');
    }

    const requiredHeaders = ['EMAIL', 'COUNTRY', 'FULLNAME', 'JOB'];
    const firstRow = csvData[0];

    for (const header of requiredHeaders) {
      if (!(header in firstRow)) {
        throw new BadRequestException(
          `Missing required header: ${header}. Expected headers: ${requiredHeaders.join(', ')}`,
        );
      }
    }

    csvData.forEach((row, index) => {
      if (!row.EMAIL || !row.EMAIL.includes('@')) {
        throw new BadRequestException(
          `Invalid email at row ${index + 1}: ${row.EMAIL}`,
        );
      }
      if (!row.FULLNAME || row.FULLNAME.trim().length === 0) {
        throw new BadRequestException(`Missing fullname at row ${index + 1}`);
      }
    });
  }

  async getCampUsers(campId: string): Promise<CampUserResponseDTO[]> {
    const users = await this.campUserModel
      .find({ campId, deletedAt: { $exists: false } })
      .lean()
      .exec();

    return users.map((user) =>
      plainToInstance(CampUserResponseDTO, user, {
        excludeExtraneousValues: true,
      }),
    );
  }

  async getTimeAnalysis(campaignId: string): Promise<TimeAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const avgTimeToOpen = await this.calculateAvgTimeToOpen(campaignId);
    const avgTimeToConvert = await this.calculateAvgTimeToConvert(campaignId);
    const openTrend = await this.calculateOpenTrend(campaignId);
    const conversionTrend = await this.calculateConversionTrend(campaignId);

    return plainToInstance(
      TimeAnalysisResponseDTO,
      {
        avgTimeToOpen,
        avgTimeToConvert,
        openTrend,
        conversionTrend,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  private async calculateAvgTimeToOpen(campaignId: string): Promise<any> {
    const timeData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.sentAt': { $exists: true },
          'emailTracking.openedAt': { $exists: true },
          'emailTracking.status': {
            $in: [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
          },
        },
      },
      {
        $addFields: {
          timeToOpen: {
            $subtract: ['$emailTracking.openedAt', '$emailTracking.sentAt'],
          },
        },
      },
      {
        $group: {
          _id: '$emailTracking.emailType',
          avgTimeToOpenMs: { $avg: '$timeToOpen' },
        },
      },
    ]);

    const result: any = {};

    timeData.forEach((data) => {
      if (!data._id || !data.avgTimeToOpenMs) return;

      const emailType = data._id;
      const responseKey = this.getResponseKey(emailType);

      if (!responseKey) return;

      const avgTimeMs = data.avgTimeToOpenMs;
      result[responseKey] = this.formatDuration(avgTimeMs);
    });

    // Đảm bảo có ít nhất start và end
    if (!result.start) {
      result.start = '0 minutes';
    }
    if (!result.end) {
      result.end = '0 minutes';
    }

    return result;
  }

  private async calculateAvgTimeToConvert(campaignId: string): Promise<string> {
    const conversionData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
        },
      },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: '$userId',
          startTime: {
            $min: {
              $cond: [
                { $eq: ['$emailTracking.emailType', EMAIL_TYPE.START] },
                '$emailTracking.sentAt',
                null,
              ],
            },
          },
          completedTime: { $first: '$completedAt' },
        },
      },
      {
        $match: {
          startTime: { $exists: true, $ne: null },
          completedTime: { $exists: true, $ne: null },
        },
      },
      {
        $addFields: {
          timeToConvert: {
            $subtract: ['$completedTime', '$startTime'],
          },
        },
      },
      {
        $group: {
          _id: null,
          avgTimeToConvertMs: { $avg: '$timeToConvert' },
        },
      },
    ]);

    const avgTimeMs = conversionData[0]?.avgTimeToConvertMs || 0;
    return this.formatDuration(avgTimeMs);
  }

  private async calculateOpenTrend(
    campaignId: string,
  ): Promise<TrendDataDTO[]> {
    const trendData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.openedAt': { $exists: true },
          'emailTracking.status': {
            $in: [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
          },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$emailTracking.openedAt',
            },
          },
          opens: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          opens: 1,
        },
      },
    ]);

    return trendData.map((item) =>
      plainToInstance(TrendDataDTO, item, {
        excludeExtraneousValues: true,
      }),
    );
  }

  private async calculateConversionTrend(
    campaignId: string,
  ): Promise<TrendDataDTO[]> {
    const trendData = await this.campUserModel.aggregate([
      {
        $match: {
          campId: campaignId,
          status: CAMP_USER_STATUS.COMPLETED,
          completedAt: { $exists: true },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$completedAt',
            },
          },
          conversions: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
      {
        $project: {
          _id: 0,
          date: '$_id',
          conversions: 1,
        },
      },
    ]);

    return trendData.map((item) =>
      plainToInstance(TrendDataDTO, item, {
        excludeExtraneousValues: true,
      }),
    );
  }

  private formatDuration(milliseconds: number): string {
    if (milliseconds <= 0) return '0 minutes';

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      const remainingHours = hours % 24;
      if (remainingHours > 0) {
        return `${days} day${days > 1 ? 's' : ''} ${remainingHours} hour${remainingHours > 1 ? 's' : ''}`;
      }
      return `${days} day${days > 1 ? 's' : ''}`;
    }

    if (hours > 0) {
      const remainingMinutes = minutes % 60;
      if (remainingMinutes > 0) {
        return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
      }
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    }

    if (minutes > 0) {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }

    return `${seconds} second${seconds > 1 ? 's' : ''}`;
  }

  async getErrorAnalysis(
    campaignId: string,
  ): Promise<ErrorAnalysisResponseDTO> {
    const camp = await this.campModel.findById(campaignId).lean().exec();
    if (!camp) {
      throw new BadRequestException('Campaign not found');
    }

    const totalEmailsSent = await this.getTotalEmailsSent(campaignId);
    const failureData = await this.getFailureAnalysis(campaignId);
    const bounceRate = await this.calculateBounceRate(
      campaignId,
      totalEmailsSent,
    );
    const nonInteractiveUsers = await this.getNonInteractiveUsers(campaignId);

    return plainToInstance(
      ErrorAnalysisResponseDTO,
      {
        failedEmails: failureData.total,
        failureReasons: failureData.reasons,
        bounceRate,
        nonInteractiveUsers,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  private async getTotalEmailsSent(campaignId: string): Promise<number> {
    const result = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': {
            $in: [
              EMAIL_STATUS.SENT,
              EMAIL_STATUS.DELIVERED,
              EMAIL_STATUS.OPENED,
              EMAIL_STATUS.CLICKED,
              EMAIL_STATUS.BOUNCED,
              EMAIL_STATUS.FAILED,
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          totalSent: { $sum: 1 },
        },
      },
    ]);

    return result[0]?.totalSent || 0;
  }

  private async getFailureAnalysis(
    campaignId: string,
  ): Promise<{ total: number; reasons: any }> {
    const failureData = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': {
            $in: [EMAIL_STATUS.BOUNCED, EMAIL_STATUS.FAILED],
          },
        },
      },
      {
        $addFields: {
          failureCategory: {
            $switch: {
              branches: [
                {
                  case: {
                    $eq: ['$emailTracking.status', EMAIL_STATUS.BOUNCED],
                  },
                  then: 'bounced',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /invalid|email|address/i,
                        },
                      },
                    ],
                  },
                  then: 'invalidEmail',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /server|timeout|connection/i,
                        },
                      },
                    ],
                  },
                  then: 'serverError',
                },
                {
                  case: {
                    $and: [
                      { $eq: ['$emailTracking.status', EMAIL_STATUS.FAILED] },
                      {
                        $regexMatch: {
                          input: {
                            $ifNull: ['$emailTracking.failureReason', ''],
                          },
                          regex: /spam|blocked/i,
                        },
                      },
                    ],
                  },
                  then: 'spamBlocked',
                },
              ],
              default: 'other',
            },
          },
        },
      },
      {
        $group: {
          _id: '$failureCategory',
          count: { $sum: 1 },
        },
      },
    ]);

    const reasons = {
      invalidEmail: 0,
      serverError: 0,
      other: 0,
      bounced: 0,
      spamBlocked: 0,
    };

    let total = 0;

    failureData.forEach((item) => {
      const category = item._id;
      const count = item.count;
      total += count;

      if (category in reasons) {
        reasons[category] = count;
      } else {
        reasons.other += count;
      }
    });

    return { total, reasons };
  }

  private async calculateBounceRate(
    campaignId: string,
    totalEmailsSent: number,
  ): Promise<number> {
    if (totalEmailsSent === 0) return 0;

    const bouncedCount = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      { $unwind: { path: '$emailTracking', preserveNullAndEmptyArrays: true } },
      {
        $match: {
          'emailTracking.status': EMAIL_STATUS.BOUNCED,
        },
      },
      {
        $group: {
          _id: null,
          bounced: { $sum: 1 },
        },
      },
    ]);

    const bounced = bouncedCount[0]?.bounced || 0;
    return Math.round((bounced / totalEmailsSent) * 1000) / 10;
  }

  private async getNonInteractiveUsers(campaignId: string): Promise<number> {
    const result = await this.campUserModel.aggregate([
      { $match: { campId: campaignId } },
      {
        $addFields: {
          hasInteraction: {
            $anyElementTrue: {
              $map: {
                input: '$emailTracking',
                as: 'tracking',
                in: {
                  $in: [
                    '$$tracking.status',
                    [EMAIL_STATUS.OPENED, EMAIL_STATUS.CLICKED],
                  ],
                },
              },
            },
          },
        },
      },
      {
        $match: {
          hasInteraction: { $ne: true },
        },
      },
      {
        $group: {
          _id: null,
          nonInteractiveCount: { $sum: 1 },
        },
      },
    ]);

    return result[0]?.nonInteractiveCount || 0;
  }

  // Helper method để tạo initial email tracking cho user
  private createInitialEmailTracking(): any[] {
    return [
      {
        emailType: EMAIL_TYPE.START,
        status: EMAIL_STATUS.PENDING,
        openCount: 0,
        clickCount: 0,
      },
    ];
  }
}
