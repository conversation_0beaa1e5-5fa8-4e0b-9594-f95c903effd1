import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { CampService } from './camp.service';
import {
  CreateCampDTO,
  GetCampQueryDTO,
  GetCampResponseDTO,
  GetCampsResponseDTO,
} from './dto/camp.dto';

@ApiTags('Client Camps')
@Controller('client/camps')
@UseGuards(GetEmailGuard)
export class ClientCampController {
  constructor(private readonly campService: CampService) {}

  @Get()
  async getCamps(
    @Query() getCampsQueryDTO: GetCampQueryDTO,
  ): Promise<GetCampsResponseDTO> {
    return this.campService.getCamps(getCampsQueryDTO);
  }

  @Get(':id')
  async getCampDetail(@Param('id') id: string): Promise<GetCampResponseDTO> {
    return this.campService.getCampDetail(id);
  }

  @Post()
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter: (req, file, callback) => {
        if (!file.originalname.match(/\.csv$/)) {
          return callback(
            new BadRequestException('Only .csv files are allowed!'),
            false,
          );
        }
        callback(null, true);
      },
    }),
  )
  async createCamp(
    @Body() createCampDTO: CreateCampDTO,
    @UploadedFile() file: Express.Multer.File,
  ): Promise<GetCampResponseDTO> {
    return this.campService.createCamp(createCampDTO, file);
  }

  @Get(':id/download')
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } = await this.campService.downloadFile(id);

    res.set({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="file"`,
    });

    stream.pipe(res);
  }
}
