import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from 'class-validator';
import {
  EMAIL_STATUS,
  CAMP_USER_STATUS,
  isValidEmailType,
} from 'src/shared/constants/camp.constant';

export function IsValidEmailType(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isValidEmailType',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          return typeof value === 'string' && isValidEmailType(value);
        },
        defaultMessage(args: ValidationArguments) {
          return 'Email type must be "start", "end", or "other_X" where X is a number (e.g., "other_1", "other_2")';
        },
      },
    });
  };
}

export class EmailTrackingDTO {
  @ApiProperty({
    description:
      'Type of email in the sequence. Can be "start", "end", or "other_X" where X is a number',
    example: 'start',
    examples: {
      start: { value: 'start', description: 'First email in sequence' },
      end: { value: 'end', description: 'Last email in sequence' },
      other_1: { value: 'other_1', description: 'Second email in sequence' },
      other_2: { value: 'other_2', description: 'Third email in sequence' },
    },
  })
  @Expose()
  @IsValidEmailType()
  emailType: string;

  @ApiProperty({
    description: 'Status of the email',
    enum: EMAIL_STATUS,
    example: EMAIL_STATUS.SENT,
  })
  @Expose()
  @IsEnum(EMAIL_STATUS)
  status: EMAIL_STATUS;

  @ApiProperty({
    description: 'Date when email was sent',
    example: '2025-01-15T10:30:00.000Z',
    required: false,
  })
  @Expose()
  @IsOptional()
  sentAt?: Date;

  @ApiProperty({
    description: 'Date when email was delivered',
    example: '2025-01-15T10:31:00.000Z',
    required: false,
  })
  @Expose()
  @IsOptional()
  deliveredAt?: Date;

  @ApiProperty({
    description: 'Date when email was opened',
    example: '2025-01-15T11:00:00.000Z',
    required: false,
  })
  @Expose()
  @IsOptional()
  openedAt?: Date;

  @ApiProperty({
    description: 'Date when email was clicked',
    example: '2025-01-15T11:05:00.000Z',
    required: false,
  })
  @Expose()
  @IsOptional()
  clickedAt?: Date;

  @ApiProperty({
    description: 'Number of times email was opened',
    example: 3,
  })
  @Expose()
  openCount: number;

  @ApiProperty({
    description: 'Number of times email was clicked',
    example: 1,
  })
  @Expose()
  clickCount: number;
}

export class CreateCampUserDTO {
  @ApiProperty({
    description: 'User ID',
    example: 'user123',
  })
  @IsString()
  userId: string;

  @ApiProperty({
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @IsString()
  campId: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
    required: false,
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  lastName?: string;
}

export class UpdateEmailTrackingDTO {
  @ApiProperty({
    description:
      'Type of email being tracked. Can be "start", "end", or "other_X" where X is a number',
    example: 'start',
    examples: {
      start: { value: 'start', description: 'First email in sequence' },
      end: { value: 'end', description: 'Last email in sequence' },
      other_1: { value: 'other_1', description: 'Second email in sequence' },
      other_2: { value: 'other_2', description: 'Third email in sequence' },
    },
  })
  @IsValidEmailType()
  emailType: string;

  @ApiProperty({
    description: 'New status of the email',
    enum: Object.values(EMAIL_STATUS),
    enumName: 'EMAIL_STATUS',
    example: EMAIL_STATUS.OPENED,
  })
  @IsEnum(EMAIL_STATUS)
  status: EMAIL_STATUS;

  @ApiProperty({
    description: 'Timestamp of the event',
    example: '2025-01-15T11:00:00.000Z',
    required: false,
  })
  @IsOptional()
  timestamp?: Date;
}

export class CampUserResponseDTO {
  @ApiProperty({
    description: 'Camp user ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'User ID',
    example: 'user123',
  })
  @Expose()
  userId: string;

  @ApiProperty({
    description: 'Campaign ID',
    example: '683052ed8a47891f9d8ff88b',
  })
  @Expose()
  campId: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @Expose()
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @Expose()
  lastName?: string;

  @ApiProperty({
    description: 'User status in campaign',
    enum: CAMP_USER_STATUS,
    example: CAMP_USER_STATUS.ACTIVE,
  })
  @Expose()
  status: CAMP_USER_STATUS;

  @ApiProperty({
    description: 'Email tracking information',
    type: [EmailTrackingDTO],
  })
  @Expose()
  @Type(() => EmailTrackingDTO)
  @ValidateNested({ each: true })
  emailTracking: EmailTrackingDTO[];

  @ApiProperty({
    description: 'Date when user was enrolled in campaign',
    example: '2025-01-15T10:00:00.000Z',
  })
  @Expose()
  enrolledAt: Date;

  @ApiProperty({
    description: 'Date when last email was sent',
    example: '2025-01-15T10:30:00.000Z',
  })
  @Expose()
  lastEmailSentAt?: Date;

  @ApiProperty({
    description: 'Date when campaign was completed for this user',
    example: '2025-01-20T15:00:00.000Z',
  })
  @Expose()
  completedAt?: Date;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-01-15T10:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-01-15T11:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;
}
