import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  User<PERSON>hitelist,
  UserWhitelistSchema,
} from 'src/shared/entities/auth/user-whitelist.entity';
import { User, UserSchema } from 'src/shared/entities/auth/user.entity';
import { AuthModule } from '../auth/auth.module';
import { TelegramService } from './telegram.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: User.name,
          schema: UserSchema,
        },
        {
          name: UserWhitelist.name,
          schema: UserWhitelistSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  providers: [TelegramService],
  exports: [TelegramService],
})
export class TelegramModule {}
