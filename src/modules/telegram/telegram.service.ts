import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { isNil } from 'lodash';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import { ROLE } from 'src/shared/constants/auth.constant';
import {
  UserWhitelist,
  UserWhitelistDocument,
} from 'src/shared/entities/auth/user-whitelist.entity';
import { User, UserDocument } from 'src/shared/entities/auth/user.entity';
import { Telegraf } from 'telegraf';
import { AuthService } from '../auth/auth.service';
import { UserResponseDTO } from '../auth/dto/login.dto';

@Injectable()
export class TelegramService implements OnModuleInit {
  private bot: Telegraf;
  private botNotifyUser: Telegraf;

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
    @InjectModel(User.name, AdminDB)
    private readonly userModel: Model<UserDocument>,
    @InjectModel(UserWhitelist.name, AdminDB)
    private readonly userWhitelistModel: Model<UserWhitelistDocument>,
  ) {}

  onModuleInit() {
    const token = this.configService.get<string>('TELEGRAM_BOT_TOKEN', '');
    const tokenNotifyUser = this.configService.get<string>(
      'TELEGRAM_BOT_TOKEN_NOTIFY_USER',
      '',
    );

    this.bot = new Telegraf(token);
    this.botNotifyUser = new Telegraf(tokenNotifyUser);

    this.bot.start(async (ctx) => {
      await this.handleStartCommand(ctx);
    });

    this.bot.command('login', async (ctx) => {
      await this.handleLoginCommand(ctx);
    });

    this.bot.launch().catch((err) => {
      console.error(err);
    });
  }

  // Hàm tiện ích để thoát ký tự đặc biệt trong Markdown
  private escapeMarkdown(text: string): string {
    if (!text) return '';
    return text.replace(/([_*[\]()~`>#+\-=|{}.!])/g, '\\$1');
  }

  async handleLoginCommand(ctx: any) {
    const username = ctx.message.text.split(' ')[1];
    const password = ctx.message.text.split(' ')[2];

    if (isNil(username) || isNil(password)) {
      await ctx.reply('Please provide username and password.');

      const telegramUsername = this.escapeMarkdown(
        ctx.from.username || 'unknown',
      );
      await this.sendNotificationToGroup(
        `Login admin failed: Missing username or password. **${telegramUsername}**`,
      );
      return;
    }

    try {
      const token = await this.authService.loginWithAccount(username, password);
      await this.sendMessageWithInlineKeyboard(ctx, token);

      const telegramUsername = this.escapeMarkdown(
        ctx.from.username || 'unknown',
      );
      await this.sendNotificationToGroup(
        `User **${telegramUsername}** has logged in successfully.`,
      );
    } catch (err) {
      console.log('Login error:', err);
      await ctx.reply('Login failed. Please check your credentials.');

      const telegramUsername = this.escapeMarkdown(
        ctx.from.username || 'unknown',
      );
      await this.sendNotificationToGroup(
        `Login attempt failed for user **${telegramUsername}**.`,
      );
    }
  }

  async handleStartCommand(ctx: any, code?: string) {
    const telegramUser = ctx.from;

    const userExisted = await this.userModel.findOne({
      telegramId: telegramUser.id,
    });

    if (isNil(userExisted)) {
      const telegramUsername = this.escapeMarkdown(
        telegramUser.username || 'unknown',
      );

      try {
        const isUserWhitelist = await this.userWhitelistModel.findOne({
          username: telegramUsername,
        });

        if (!isUserWhitelist) {
          await ctx.reply(
            'You are not allowed to use this bot. Please contact the admin.',
          );
          return;
        }
      } catch (err) {
        console.log('Whitelist check error:', err);
        await ctx.reply(
          'You are not allowed to use this bot. Please contact the admin.',
        );
        await this.sendNotificationToGroup(
          `User **${telegramUsername}** is not allowed to use the bot.`,
        );
        return;
      }
    }

    const user: UserResponseDTO = {
      telegramId: telegramUser.id,
      firstName: telegramUser.first_name || '',
      lastName: telegramUser.last_name || '',
      username: telegramUser.username || '',
      languageCode: telegramUser.language_code || '',
      role: ROLE.USER,
    };

    const token = await this.authService.generateTempToken(user);
    await this.sendMessageWithInlineKeyboard(ctx, token);
  }

  async sendMessageWithInlineKeyboard(ctx: any, token: string) {
    const webAdminBaseUrl =
      this.configService.get<string>('WEB_ADMIN_BASE_URL');
    await ctx
      .reply('Please login to the dashboard to continue.', {
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: 'Login to Dashboard',
                url: `${webAdminBaseUrl}/login?token=${token}`,
              },
            ],
          ],
        },
        parse_mode: 'Markdown',
      })
      .catch((err) => {
        console.error(err);
      });
  }

  async sendNotificationToGroup(message: string) {
    const groupId = this.configService.get<string>('TELEGRAM_GROUP_NOTIFY_ID');
    if (!groupId) {
      console.error('Group ID is not configured.');
      return;
    }
    try {
      await this.botNotifyUser.telegram.sendMessage(groupId, message, {
        parse_mode: 'Markdown',
      });
    } catch (err) {
      console.error('Failed to send notification to group:', err);
    }
  }
}
