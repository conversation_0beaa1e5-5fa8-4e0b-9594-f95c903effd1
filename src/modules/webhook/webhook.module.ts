import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  Webhook,
  WebhookSchema,
} from 'src/shared/entities/webhook/webhook.entity';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: Webhook.name,
          schema: WebhookSchema,
        },
        { name: ValidToken.name, schema: ValidTokenSchema },
        { name: GroupUserConnection.name, schema: GroupUserConnectionSchema },
      ],
      AdminDB,
    ),
  ],
  controllers: [WebhookController],
  providers: [WebhookService],
  exports: [WebhookService],
})
export class WebhookModule {}
