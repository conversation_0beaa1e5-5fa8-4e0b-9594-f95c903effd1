import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { User } from 'src/shared/entities/auth/user.entity';
import {
  Webhook,
  WebhookDocument,
} from 'src/shared/entities/webhook/webhook.entity';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import { AdminDB } from 'src/config';
import {
  CreateWebhookDTO,
  GetWebhookQueryDTO,
  GetWebhooksResponseDTO,
  UpdateWebhookDTO,
  WebhookResponseDTO,
} from './dto/webhook.dto';

@Injectable()
export class WebhookService {
  constructor(
    @InjectModel(Webhook.name, AdminDB)
    private readonly webhookModel: AuthModel<WebhookDocument>,
  ) {}

  async getWebhooks(
    getWebhooksQueryDTO: GetWebhookQueryDTO,
  ): Promise<GetWebhooksResponseDTO> {
    const { skip = 0, limit = 10, name, webhookUrl } = getWebhooksQueryDTO;

    const query: Record<string, any> = {};

    if (name) {
      query.name = { $regex: name, $options: 'i' };
    }

    if (webhookUrl) {
      query.webhookUrl = { $regex: webhookUrl, $options: 'i' };
    }

    const [webhooks, total] = await Promise.all([
      this.webhookModel
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 })
        .lean(),
      this.webhookModel.countDocuments(query),
    ]);

    const data = webhooks.map((webhook) =>
      plainToInstance(WebhookResponseDTO, webhook, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async getWebhookDetail(id: string): Promise<WebhookResponseDTO> {
    const webhook = await this.webhookModel.findOne({ _id: id }).lean().exec();

    if (!webhook) {
      throw new BadRequestException('Webhook not found');
    }

    return plainToInstance(WebhookResponseDTO, webhook, {
      excludeExtraneousValues: true,
    });
  }

  async createWebhook(
    createWebhookDTO: CreateWebhookDTO,
    authUser: User,
  ): Promise<WebhookResponseDTO> {
    const { webhookUrl, name, description } = createWebhookDTO;

    // Check if webhook URL already exists
    const existingWebhook = await this.webhookModel
      .findOne({ webhookUrl })
      .lean()
      .exec();

    if (existingWebhook) {
      throw new BadRequestException('Webhook URL already exists');
    }

    // Check if webhook name already exists
    const existingName = await this.webhookModel
      .findOne({ name })
      .lean()
      .exec();

    if (existingName) {
      throw new BadRequestException('Webhook name already exists');
    }

    const newWebhook = new this.webhookModel({
      webhookUrl,
      name,
      description,
      createdAt: new Date(),
      createdBy: authUser.telegramId,
      createdByUsername: authUser.username,
    });

    const savedWebhook = await newWebhook.save();

    return plainToInstance(WebhookResponseDTO, savedWebhook.toObject(), {
      excludeExtraneousValues: true,
    });
  }

  async updateWebhook(
    id: string,
    updateWebhookDTO: UpdateWebhookDTO,
    authUser: User,
  ): Promise<WebhookResponseDTO> {
    const { webhookUrl, name, description } = updateWebhookDTO;

    const webhook = await this.webhookModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean();

    if (!webhook) {
      throw new BadRequestException('Webhook not found');
    }

    const updateData: Partial<WebhookDocument> = {};

    // Check if webhook URL is being updated and doesn't conflict
    if (webhookUrl && webhookUrl !== (webhook as any).webhookUrl) {
      const existingWebhook = await this.webhookModel
        .findOne({ webhookUrl, _id: { $ne: id } })
        .lean()
        .exec();

      if (existingWebhook) {
        throw new BadRequestException('Webhook URL already exists');
      }
      updateData.webhookUrl = webhookUrl;
    }

    // Check if webhook name is being updated and doesn't conflict
    if (name && name !== (webhook as any).name) {
      const existingName = await this.webhookModel
        .findOne({ name, _id: { $ne: id } })
        .lean()
        .exec();

      if (existingName) {
        throw new BadRequestException('Webhook name already exists');
      }
      updateData.name = name;
    }

    if (description !== undefined) {
      updateData.description = description;
    }

    if (Object.keys(updateData).length === 0) {
      throw new BadRequestException('No fields to update');
    }

    const updatedWebhook = await this.webhookModel.findOneAndUpdateWithAuth(
      { _id: id },
      {
        ...updateData,
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      },
      { new: true },
      authUser,
    );

    return plainToInstance(WebhookResponseDTO, updatedWebhook, {
      excludeExtraneousValues: true,
    });
  }

  async deleteWebhook(
    id: string,
    authUser: User,
  ): Promise<{ success: boolean }> {
    const webhook = await this.webhookModel
      .findOneWithAuth({ _id: id }, authUser)
      .lean()
      .exec();

    if (!webhook) {
      throw new BadRequestException('Webhook not found');
    }

    await this.webhookModel
      .findByIdAndUpdate(id, {
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
        deletedAt: new Date(),
        deletedBy: authUser.telegramId,
      })
      .lean()
      .exec();

    return { success: true };
  }

  async getWebhookByUrl(webhookUrl: string): Promise<WebhookDocument | null> {
    return this.webhookModel.findOne({ webhookUrl }).lean().exec();
  }

  async getWebhookById(id: string): Promise<WebhookDocument | null> {
    return this.webhookModel.findById(id).lean().exec();
  }
}
