import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { WebhookService } from './webhook.service';
import {
  CreateWebhookDTO,
  GetWebhookQueryDTO,
  GetWebhooksResponseDTO,
  UpdateWebhookDTO,
  WebhookResponseDTO,
} from './dto/webhook.dto';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';

@Controller('webhooks')
@ApiTags('Webhooks')
@UseGuards(AuthGuard)
@ApiBearerAuth('JWT-auth')
export class WebhookController {
  constructor(private readonly webhookService: WebhookService) {}

  @Get()
  @ApiOperation({
    summary: 'Get webhooks',
    description:
      'Retrieve a paginated list of webhooks with optional filtering',
  })
  @ApiQuery({
    name: 'skip',
    required: false,
    description: 'Number of records to skip',
    example: 0,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of records to return',
    example: 10,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'Filter by webhook name',
    example: 'Campaign',
  })
  @ApiQuery({
    name: 'webhookUrl',
    required: false,
    description: 'Filter by webhook URL',
    example: 'api.example.com',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhooks retrieved successfully',
    type: GetWebhooksResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getWebhooks(
    @Query() getWebhooksQueryDTO: GetWebhookQueryDTO,
  ): Promise<GetWebhooksResponseDTO> {
    return this.webhookService.getWebhooks(getWebhooksQueryDTO);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get webhook detail',
    description: 'Retrieve detailed information about a specific webhook',
  })
  @ApiParam({
    name: 'id',
    description: 'Webhook ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook retrieved successfully',
    type: WebhookResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Webhook not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getWebhookDetail(@Param('id') id: string): Promise<WebhookResponseDTO> {
    return this.webhookService.getWebhookDetail(id);
  }

  @Post()
  @ApiOperation({
    summary: 'Create webhook',
    description: 'Create a new webhook with unique URL and name',
  })
  @ApiResponse({
    status: 201,
    description: 'Webhook created successfully',
    type: WebhookResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Webhook URL or name already exists',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async createWebhook(
    @Body() createWebhookDTO: CreateWebhookDTO,
    @RequestUser() authUser: User,
  ): Promise<WebhookResponseDTO> {
    return this.webhookService.createWebhook(createWebhookDTO, authUser);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update webhook',
    description: 'Update an existing webhook',
  })
  @ApiParam({
    name: 'id',
    description: 'Webhook ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook updated successfully',
    type: WebhookResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Webhook not found or validation error',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async updateWebhook(
    @Param('id') id: string,
    @Body() updateWebhookDTO: UpdateWebhookDTO,
    @RequestUser() authUser: User,
  ): Promise<WebhookResponseDTO> {
    return this.webhookService.updateWebhook(id, updateWebhookDTO, authUser);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete webhook',
    description: 'Soft delete a webhook (sets deletedAt timestamp)',
  })
  @ApiParam({
    name: 'id',
    description: 'Webhook ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Webhook not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async deleteWebhook(
    @Param('id') id: string,
    @RequestUser() authUser: User,
  ): Promise<{ success: boolean }> {
    return this.webhookService.deleteWebhook(id, authUser);
  }
}
