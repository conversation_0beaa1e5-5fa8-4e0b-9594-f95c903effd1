import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { ArrayMaxSize, ArrayMinSize, IsArray, IsString } from 'class-validator';

export class AddWhitelistDTO {
  @ApiProperty({
    description: 'Array of usernames to add to whitelist',
    example: ['user1', 'user2', 'user3'],
    type: [String],
    minItems: 1,
    maxItems: 1000,
  })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  @ArrayMaxSize(1000)
  usernames: string[];
}

export class UserWhitelistResponseDTO {
  @ApiProperty({
    description: 'Whitelist entry unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Username in whitelist',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'Date when entry was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'ID of user who created this entry',
    example: 123456789,
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;
}
