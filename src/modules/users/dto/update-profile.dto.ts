import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class UpdateProfileDTO {
  @ApiPropertyOptional({
    description: 'User first name',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiPropertyOptional({
    description: 'User last name',
    example: 'Doe',
  })
  @IsOptional()
  @IsString()
  lastName?: string;
}

export class ProfileResponseDTO {
  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
  })
  @Expose()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: '<PERSON><PERSON>',
  })
  @Expose()
  lastName: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'User credit balance',
    example: 100,
  })
  @Expose()
  credit: number;
}