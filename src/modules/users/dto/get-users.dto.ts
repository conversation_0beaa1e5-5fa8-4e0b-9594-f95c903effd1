import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsOptional,
  IsString,
  IsN<PERSON>ber,
  Min,
  Max,
} from 'class-validator';
import { ROLE } from 'src/shared/constants/auth.constant';

export class GetUsersQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: '0',
    default: '0',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: '10',
    default: '10',
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter users by username (case-insensitive partial match)',
    example: 'john_doe',
  })
  @IsString()
  @IsOptional()
  username?: string;

  @ApiPropertyOptional({
    description: 'Filter users by roles',
    example: ['admin', 'user'],
    enum: ROLE,
    isArray: true,
  })
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  roles?: ROLE[];
}

export class UserResponseDTO {
  @ApiProperty({
    description: 'User unique identifier',
    example: '507f1f77bcf86cd799439011',
  })
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @ApiProperty({
    description: 'Telegram user ID',
    example: '*********',
  })
  @Expose()
  telegramId: string;

  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @Expose()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
  })
  @Expose()
  lastName: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'User language code',
    example: 'en',
  })
  @Expose()
  languageCode: string;

  @ApiProperty({
    description: 'User role',
    enum: ROLE,
    example: ROLE.USER,
  })
  @Expose()
  role: ROLE;

  @ApiProperty({
    description: 'User credit balance',
    example: 100,
  })
  @Expose()
  credit: number;

  @ApiProperty({
    description: 'Account creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    description: 'ID of user who created this account',
    example: *********,
  })
  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @ApiPropertyOptional({
    description: 'Last update date',
    example: '2023-01-02T00:00:00.000Z',
  })
  @Expose()
  updatedAt?: Date;

  @ApiPropertyOptional({
    description: 'ID of user who last updated this account',
    example: *********,
  })
  @Expose()
  updatedBy?: number;
}

export class GetUsersResponseDTO {
  @ApiProperty({
    description: 'Array of users',
    type: [UserResponseDTO],
  })
  @Expose()
  data: Array<UserResponseDTO>;

  @ApiProperty({
    description: 'Total number of users matching the query',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}
