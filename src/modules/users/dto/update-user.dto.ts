import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsEnum,
  Max,
  Min,
} from 'class-validator';
import { ROLE } from 'src/shared/constants/auth.constant';

export class UpdateUserDTO {
  @ApiPropertyOptional({
    description: 'User role to update',
    enum: ROLE,
    example: ROLE.ADMIN,
  })
  @IsOptional()
  @IsEnum(ROLE)
  @IsNotEmpty()
  role?: ROLE;

  @ApiPropertyOptional({
    description: 'User credit amount to set',
    example: 100,
    minimum: 0,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  credit?: number;
}

export class UpdateUserResponseDTO {
  @ApiProperty({
    description: 'User first name',
    example: 'John',
  })
  @Expose()
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: '<PERSON><PERSON>',
  })
  @Expose()
  lastName: string;

  @ApiProperty({
    description: 'Username',
    example: 'john_doe',
  })
  @Expose()
  username: string;

  @ApiProperty({
    description: 'User language code',
    example: 'en',
  })
  @Expose()
  languageCode: string;

  @ApiProperty({
    description: 'User role',
    enum: ROLE,
    example: ROLE.USER,
  })
  @Expose()
  role: ROLE;

  @ApiProperty({
    description: 'User credit balance',
    example: 100,
  })
  @Expose()
  credit: number;
}

export class ChargeCreditDTO {
  @ApiProperty({
    description: 'Amount of credit to charge (deduct) from user',
    example: 10,
    minimum: 0,
    maximum: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  @Min(0)
  @Max(100)
  credit: number;
}
