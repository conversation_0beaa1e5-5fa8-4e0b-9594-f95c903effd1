import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import { ROLE } from 'src/shared/constants/auth.constant';
import {
  UserWhitelist,
  UserWhitelistDocument,
} from 'src/shared/entities/auth/user-whitelist.entity';
import { User, UserDocument } from 'src/shared/entities/auth/user.entity';
import {
  AddWhitelistDTO,
  UserWhitelistResponseDTO,
} from './dto/add-whitelist.dto';
import {
  GetUsersQueryDTO,
  GetUsersResponseDTO,
  UserResponseDTO,
} from './dto/get-users.dto';
import { UpdateUserDTO, UpdateUserResponseDTO } from './dto/update-user.dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name, AdminDB)
    private usersRepository: Model<UserDocument>,
    @InjectModel(UserWhitelist.name, AdminDB)
    private userWhitelistModel: Model<UserWhitelistDocument>,
  ) {}

  async getUsers(
    getUsersQueryDTO: GetUsersQueryDTO,
  ): Promise<GetUsersResponseDTO> {
    const { skip = 0, limit = 10, roles, username } = getUsersQueryDTO;

    const query: Record<string, any> = {};

    if (roles) {
      query.role = { $in: roles };
    }
    if (username) {
      query.username = { $regex: username, $options: 'i' };
    }

    const [users, total] = await Promise.all([
      this.usersRepository
        .find(query)
        .skip(Number(skip))
        .limit(Number(limit))
        .exec(),
      this.usersRepository.countDocuments(query).exec(),
    ]);

    const data = users.map((user) =>
      plainToInstance(UserResponseDTO, user, {
        excludeExtraneousValues: true,
      }),
    );

    return {
      data,
      total,
      skip: Number(skip),
      limit: Number(limit),
    };
  }

  async updateUser(
    userId: string,
    updateUserDTO: UpdateUserDTO,
    authUser: User,
  ): Promise<UpdateUserResponseDTO> {
    const { role } = updateUserDTO;

    if (role == ROLE.USER && authUser.role !== ROLE.SUPER_ADMIN) {
      throw new ForbiddenException(
        'You do not have permission to change the role to USER',
      );
    }
    try {
      const updatedUser = await this.usersRepository
        .findByIdAndUpdate(
          userId,
          {
            ...updateUserDTO,
            updatedAt: new Date(),
            updatedBy: authUser.telegramId,
          },
          { new: true },
        )
        .exec();
      if (!updatedUser) {
        throw new BadRequestException('User not found');
      }
      return plainToInstance(UpdateUserResponseDTO, updatedUser, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      throw new BadRequestException(
        'Invalid request. Please check the data you provided.',
        error.message,
      );
    }
  }

  async chargeCredit(
    userId: string,
    credit: number,
    authUser: User,
  ): Promise<UpdateUserResponseDTO> {
    try {
      const updatedUser = await this.usersRepository
        .findByIdAndUpdate(
          userId,
          {
            $inc: { credit: -credit },
            updatedAt: new Date(),
            updatedBy: authUser.telegramId,
          },
          { new: true },
        )
        .exec();
      if (!updatedUser) {
        throw new Error('User not found');
      }
      return plainToInstance(UpdateUserResponseDTO, updatedUser, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      throw new BadRequestException(
        'Invalid request. Please check the data you provided.',
        error.message,
      );
    }
  }

  async addWhitelist(
    addWhitelistDto: AddWhitelistDTO,
    authUser: User,
  ): Promise<UserWhitelistResponseDTO[]> {
    const { usernames } = addWhitelistDto;

    const userWhitelist = await this.usersRepository
      .find({ username: { $in: usernames } })
      .exec();

    const userNeedInserted = usernames.filter(
      (name) => !userWhitelist.some((user) => user.username === name),
    );

    const newUserWhitelist = userNeedInserted.map((name) => ({
      username: name,
      createdAt: new Date(),
      createdBy: authUser.telegramId,
      createdByUsername: authUser.username,
    }));

    const insertedUserWhitelist =
      await this.userWhitelistModel.insertMany(newUserWhitelist);

    return insertedUserWhitelist.map((user) =>
      plainToInstance(UserWhitelistResponseDTO, user, {
        excludeExtraneousValues: true,
      }),
    );
  }
}
