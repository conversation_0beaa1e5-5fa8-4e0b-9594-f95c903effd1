import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBadRequestResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { ROLE } from 'src/shared/constants/auth.constant';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { Roles } from 'src/shared/decorators/role.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { RolesGuard } from 'src/shared/guards/role.guard';
import {
  ErrorResponseDTO,
  ForbiddenErrorResponseDTO,
  NotFoundErrorResponseDTO,
  UnauthorizedErrorResponseDTO,
  ValidationErrorResponseDTO,
} from 'src/shared/dto/error-response.dto';
import {
  AddWhitelistDTO,
  UserWhitelistResponseDTO,
} from './dto/add-whitelist.dto';
import { GetUsersQueryDTO, GetUsersResponseDTO } from './dto/get-users.dto';
import {
  ChargeCreditDTO,
  UpdateUserDTO,
  UpdateUserResponseDTO,
} from './dto/update-user.dto';
import { UpdateProfileDTO, ProfileResponseDTO } from './dto/update-profile.dto';
import { UsersService } from './users.service';

@ApiTags('Users')
@ApiBearerAuth('JWT-auth')
@ApiUnauthorizedResponse({
  description: 'Unauthorized - Invalid or missing JWT token',
  type: UnauthorizedErrorResponseDTO,
})
@ApiForbiddenResponse({
  description: 'Forbidden - Insufficient permissions',
  type: ForbiddenErrorResponseDTO,
})
@Controller('users')
@Roles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MANAGER)
@UseGuards(AuthGuard, RolesGuard)
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @ApiOperation({
    summary: 'Get users list',
    description:
      'Retrieve a paginated list of users with optional filtering by username and roles',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: GetUsersResponseDTO,
  })
  @ApiBadRequestResponse({
    description: 'Invalid query parameters',
    type: ValidationErrorResponseDTO,
  })
  async getUsers(
    @Query() getUsersQueryDTO: GetUsersQueryDTO,
  ): Promise<GetUsersResponseDTO> {
    return this.usersService.getUsers(getUsersQueryDTO);
  }

  @Patch(':id')
  @ApiOperation({
    summary: 'Update user',
    description:
      'Update user role and/or credit. Only SUPER_ADMIN can change role to USER.',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID to update',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully',
    type: UpdateUserResponseDTO,
  })
  @ApiBadRequestResponse({
    description: 'Invalid user ID or update data',
    type: ErrorResponseDTO,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: NotFoundErrorResponseDTO,
  })
  @ApiForbiddenResponse({
    description: 'Insufficient permissions to change role to USER',
    type: ForbiddenErrorResponseDTO,
  })
  async updateUser(
    @Param('id') id: string,
    @Body() updateUserDTO: UpdateUserDTO,
    @RequestUser() authUser: User,
  ): Promise<UpdateUserResponseDTO> {
    return this.usersService.updateUser(id, updateUserDTO, authUser);
  }

  @Post(':id/charge-credit')
  @ApiOperation({
    summary: 'Charge user credit',
    description: 'Deduct credit from user account',
  })
  @ApiParam({
    name: 'id',
    description: 'User ID to charge credit from',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Credit charged successfully',
    type: UpdateUserResponseDTO,
  })
  @ApiBadRequestResponse({
    description: 'Invalid user ID or credit amount',
    type: ErrorResponseDTO,
  })
  @ApiNotFoundResponse({
    description: 'User not found',
    type: NotFoundErrorResponseDTO,
  })
  async chargeCredit(
    @Param('id') id: string,
    @Body() chargeCreditDTO: ChargeCreditDTO,
    @RequestUser() authUser: User,
  ): Promise<UpdateUserResponseDTO> {
    return this.usersService.chargeCredit(id, chargeCreditDTO.credit, authUser);
  }

  @Post('add-whitelist')
  @ApiOperation({
    summary: 'Add users to whitelist',
    description:
      'Add multiple usernames to the whitelist. Only new usernames will be added.',
  })
  @ApiResponse({
    status: 201,
    description: 'Users added to whitelist successfully',
    type: [UserWhitelistResponseDTO],
  })
  @ApiBadRequestResponse({
    description: 'Invalid usernames array',
    type: ValidationErrorResponseDTO,
  })
  async addWhitelist(
    @Body() body: AddWhitelistDTO,
    @RequestUser() authUser: User,
  ): Promise<UserWhitelistResponseDTO[]> {
    return this.usersService.addWhitelist(body, authUser);
  }

  @Patch('profile')
  @ApiOperation({
    summary: 'Update user profile',
    description: 'Update current user profile information',
  })
  @ApiResponse({
    status: 200,
    description: 'Profile updated successfully',
    type: ProfileResponseDTO,
  })
  @ApiBadRequestResponse({
    description: 'Invalid update data',
    type: ErrorResponseDTO,
  })
  @Roles(ROLE.ADMIN, ROLE.SUPER_ADMIN, ROLE.MANAGER, ROLE.USER)
  async updateProfile(
    @Body() updateProfileDTO: UpdateProfileDTO,
    @RequestUser() user: User,
  ): Promise<ProfileResponseDTO> {
    return this.usersService.updateProfile(user.telegramId, updateProfileDTO);
  }
}
