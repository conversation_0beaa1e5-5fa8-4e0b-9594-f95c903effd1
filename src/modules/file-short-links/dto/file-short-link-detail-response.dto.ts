import { Expose, Transform } from 'class-transformer';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';

export class FileShortLinkDetailResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  file: string;

  @Expose()
  logo: string;

  @Expose()
  prefixPass: string;

  @Expose()
  brand: string;

  @Expose()
  expirationLink: string;

  @Expose()
  shortLinkId: string;

  @Expose()
  shortLinkStatus: SHORT_LINK_STATUS;

  @Expose()
  shortLinkUrl: string;

  @Expose()
  countryCode: string;

  @Expose()
  createdAt: Date;

  @Expose()
  @Transform(({ obj }) => obj.createdByUsername || 'unknown')
  createdBy: string;

  @Expose()
  updatedAt?: Date;

  @Expose()
  updatedBy?: number;
}
