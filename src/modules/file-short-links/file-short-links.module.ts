import { <PERSON>du<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import { User, UserSchema } from 'src/shared/entities/auth/user.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  FileShortLinkMeta,
  FileShortLinkMetaSchema,
} from 'src/shared/entities/file/file-shortlink-meta.entity';
import {
  FileShortLink,
  FileShortLinkSchema,
} from 'src/shared/entities/file/file-shortlink.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { ClerkUserService } from 'src/shared/services/clerk.service';
import { AuthModule } from '../auth/auth.module';
import { ClientFileShortLinksController } from './client-file-short-links.controller';
import { FileShortLinksController } from './file-short-links.controller';
import { FileShortLinksService } from './file-short-links.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: FileShortLink.name,
          schema: FileShortLinkSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: FileShortLinkMeta.name,
          schema: FileShortLinkMetaSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [FileShortLinksController, ClientFileShortLinksController],
  providers: [FileShortLinksService, AzureStorageService, ClerkUserService],
  exports: [FileShortLinksService],
})
export class FileShortLinksModule {}
