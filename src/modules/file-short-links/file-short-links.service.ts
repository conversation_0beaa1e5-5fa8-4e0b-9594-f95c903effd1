import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToInstance } from 'class-transformer';
import { isNil } from 'lodash';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import { SYSTEM } from 'src/shared/constants/common.constant';
import {
  MAX_COUNT_CHECK,
  SHORT_LINK_STATUS,
} from 'src/shared/constants/short-link.constant';
import { User } from 'src/shared/entities/auth/user.entity';
import {
  FileShortLinkMeta,
  FileShortLinkMetaDocument,
} from 'src/shared/entities/file/file-shortlink-meta.entity';
import {
  FileShortLink,
  FileShortLinkDocument,
} from 'src/shared/entities/file/file-shortlink.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { extractSubdomain } from 'src/shared/utils/common.util';
import { FileShortLinkDetailResponseDTO } from './dto/file-short-link-detail-response.dto';
import {
  CreateFileShortLinksDTO,
  FileShortLinkMetaResponseDTO,
  FileShortLinkResponseDTO,
  GetFileShortLinkQueryDTO,
  GetFileShortLinksQueryDTO,
  GetFilesResponseDTO,
  UpdateFileShortLinksDTO,
} from './dto/file-short-links.dto';

@Injectable()
export class FileShortLinksService {
  constructor(
    @InjectModel(FileShortLink.name, AdminDB)
    private readonly fileShortLinkModel: Model<FileShortLinkDocument>,
    @InjectModel(FileShortLinkMeta.name, AdminDB)
    private readonly fileShortLinkMetaModel: Model<FileShortLinkMetaDocument>,

    private readonly azureStorageService: AzureStorageService,
  ) {}

  async createFileShortLink({
    createFileShortLinkDTO,
    file,
    authUser,
    logo,
  }: {
    createFileShortLinkDTO: CreateFileShortLinksDTO;
    file: Express.Multer.File;
    authUser?: User;
    logo?: Express.Multer.File;
  }): Promise<FileShortLinkResponseDTO> {
    const { prefixPass, brand, expirationLink, popup, countryCode } =
      createFileShortLinkDTO;
    const quantity = parseInt(createFileShortLinkDTO.quantity, 10);
    if (isNaN(quantity) || quantity <= 0) {
      throw new BadRequestException('Invalid quantity');
    }

    try {
      // Upload files
      const fileUrl = await this.azureStorageService.uploadFile(file);
      const logoUrl = logo
        ? await this.azureStorageService.uploadFile(logo)
        : undefined;

      const fileShortLink = await this.fileShortLinkModel.create({
        file: fileUrl,
        logo: logoUrl,
        prefixPass,
        quantity,
        brand: extractSubdomain(brand),
        expirationLink,
        createdAt: new Date(),
        createdBy: authUser ? authUser.telegramId : SYSTEM,
        createdByUsername: authUser ? authUser.username : SYSTEM,
      });

      const fileShortLinkMetas = await this.fileShortLinkMetaModel.insertMany(
        await Promise.all(
          Array.from({ length: quantity }).map(async () => ({
            fileShortLinkId: fileShortLink._id,
            shortLinkId: await this.generateUniqueIdShortLink(),
            shortLinkStatus: SHORT_LINK_STATUS.ACTIVATED,
            popup: !!popup,
            countryCode: countryCode ? countryCode.toUpperCase() : undefined,
            createdAt: new Date(),
            createdBy: authUser ? authUser.telegramId : SYSTEM,
            createdByUsername: authUser ? authUser.username : SYSTEM,
          })),
        ),
      );

      const fileShortLinkMetaResponse = fileShortLinkMetas.map((meta) =>
        plainToInstance(FileShortLinkMetaResponseDTO, meta, {
          excludeExtraneousValues: true,
        }),
      );

      return plainToInstance(
        FileShortLinkResponseDTO,
        {
          ...fileShortLink.toObject(),
          fileShortLinkMeta: fileShortLinkMetaResponse,
        },
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      console.error('Error creating file short link:', error);
      throw new BadRequestException(
        'Failed to create file short link',
        error.message,
      );
    }
  }

  async getFileShortLinkDetail(
    query: GetFileShortLinkQueryDTO,
  ): Promise<FileShortLinkDetailResponseDTO> {
    const { brand, shortLinkId } = query;

    const fileShortLinkMeta = await this.fileShortLinkMetaModel
      .findOne({
        shortLinkId,
      })
      .lean();

    if (isNil(fileShortLinkMeta)) {
      throw new BadRequestException('File short link not found');
    }

    const file = await this.fileShortLinkModel
      .findOne({
        _id: fileShortLinkMeta.fileShortLinkId,
        brand: extractSubdomain(brand),
      })
      .lean();

    if (isNil(file)) {
      throw new BadRequestException('File not found');
    }

    return plainToInstance(
      FileShortLinkDetailResponseDTO,
      {
        ...file,
        shortLinkId: fileShortLinkMeta.shortLinkId,
        shortLinkStatus: fileShortLinkMeta.shortLinkStatus,
        countryCode: fileShortLinkMeta.countryCode,
        shortLinkUrl: fileShortLinkMeta.shortLinkUrl,
      },
      {
        excludeExtraneousValues: true,
      },
    );
  }

  async getFileShortLinks(
    query: GetFileShortLinksQueryDTO,
  ): Promise<GetFilesResponseDTO> {
    const { skip = 0, limit = 10, brand } = query;

    let queryCondition = {};

    if (!isNil(brand)) {
      queryCondition['brand'] = {
        $regex: new RegExp(extractSubdomain(extractSubdomain(brand)), 'i'),
      };
    }

    const [files, total] = await Promise.all([
      this.fileShortLinkModel
        .find(queryCondition)
        .skip(Number(skip))
        .limit(Number(limit))
        .sort({ createdAt: -1 })
        .lean(),
      this.fileShortLinkModel.countDocuments(queryCondition),
    ]);

    const data = await Promise.all(
      files.map(async (file) => {
        const fileShortLinkMetas = await this.fileShortLinkMetaModel
          .find({ fileShortLinkId: file._id })
          .lean();
        const fileShortLinkMetaResponse = fileShortLinkMetas.map((meta) =>
          plainToInstance(FileShortLinkMetaResponseDTO, meta, {
            excludeExtraneousValues: true,
          }),
        );

        return plainToInstance(
          FileShortLinkResponseDTO,
          {
            ...file,
            fileShortLinkMeta: fileShortLinkMetaResponse,
          },
          {
            excludeExtraneousValues: true,
          },
        );
      }),
    );

    return {
      data,
      total,
      limit: Number(limit),
      skip: Number(skip),
    };
  }

  async downloadFile(
    fileId: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const file = await this.fileShortLinkModel.findById(fileId);
      if (!file) {
        throw new BadRequestException('File not found');
      }

      return this.azureStorageService.downloadFile(file.file);
    } catch (error) {
      throw new BadRequestException('Failed to download file', error.message);
    }
  }

  private generateRandomString(length: number = 6): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  async generateUniqueIdShortLink(): Promise<string> {
    const result = this.generateRandomString();

    const existing = await this.fileShortLinkModel.findOne({
      shortLinkId: result,
    });

    if (!isNil(existing)) {
      return this.generateUniqueIdShortLink();
    }

    return result;
  }

  async deleteFileShortLink(
    id: string,
    authUser: User,
  ): Promise<FileShortLinkResponseDTO> {
    const file = await this.fileShortLinkModel.findById(id);
    if (isNil(file)) {
      throw new BadRequestException('File not found');
    }

    file.deletedAt = new Date();
    file.deletedBy = authUser.telegramId;
    await file.save();

    await this.fileShortLinkMetaModel.updateMany(
      { fileShortLinkId: file._id },
      {
        $set: {
          deletedAt: new Date(),
          deletedBy: authUser.telegramId,
        },
      },
    );

    return plainToInstance(FileShortLinkResponseDTO, file, {
      excludeExtraneousValues: true,
    });
  }

  async updateFileShortLink(
    fileId: string,
    updateFileShortLinkDTO: UpdateFileShortLinksDTO,
    authUser: User,
  ): Promise<FileShortLinkResponseDTO> {
    const {
      prefixPass,
      brand,
      expirationLink,
      popup,
      countryCode,
      shortLinkStatus,
    } = updateFileShortLinkDTO;

    try {
      const file = await this.fileShortLinkModel.findById(fileId);
      if (isNil(file)) {
        throw new BadRequestException('File not found');
      }

      if (prefixPass) {
        file.prefixPass = prefixPass;
      }
      if (brand) {
        file.brand = extractSubdomain(brand);
      }
      if (expirationLink) {
        file.expirationLink = expirationLink;
      }
      await file.save();

      const dataUpdated: Partial<FileShortLinkMeta> = {
        updatedAt: new Date(),
        updatedBy: authUser.telegramId,
      };

      if (!isNil(popup)) {
        dataUpdated.popup = !!popup;
      }
      if (countryCode) {
        dataUpdated.countryCode = countryCode.toUpperCase();
      }
      if (shortLinkStatus) {
        dataUpdated.shortLinkStatus = shortLinkStatus;
      }

      await this.fileShortLinkMetaModel.updateMany(
        { fileShortLinkId: file._id },
        {
          $set: dataUpdated,
        },
      );

      const updatedFileShortLinkMetas = await this.fileShortLinkMetaModel
        .find({ fileShortLinkId: file._id })
        .lean();

      const fileShortLinkMetaResponse = updatedFileShortLinkMetas.map((meta) =>
        plainToInstance(FileShortLinkMetaResponseDTO, meta, {
          excludeExtraneousValues: true,
        }),
      );

      return plainToInstance(
        FileShortLinkResponseDTO,
        {
          ...file.toObject(),
          fileShortLinkMeta: fileShortLinkMetaResponse,
        },
        {
          excludeExtraneousValues: true,
        },
      );
    } catch (error) {
      console.error('Error updating file short link:', error);
      throw new BadRequestException('Failed to update file short link');
    }
  }

  async checkStatusShortLink(shortLinkId: string): Promise<{
    success: boolean;
    expirationLink?: string;
  }> {
    const fileShortLinkMeta = await this.fileShortLinkMetaModel.findOne({
      shortLinkId,
    });

    if (isNil(fileShortLinkMeta)) {
      throw new BadRequestException('Short link not found');
    }

    const fileShortLink = await this.fileShortLinkModel.findById(
      fileShortLinkMeta.fileShortLinkId,
    );

    if (isNil(fileShortLink)) {
      throw new BadRequestException('File not found');
    }

    if (fileShortLinkMeta.countCheck >= MAX_COUNT_CHECK) {
      await this.fileShortLinkMetaModel.updateOne(
        { shortLinkId },
        { shortLinkStatus: SHORT_LINK_STATUS.BANNED },
      );

      return {
        success: false,
        expirationLink: fileShortLink.expirationLink,
      };
    }

    await this.fileShortLinkMetaModel.updateOne(
      { shortLinkId },
      { $inc: { countCheck: 1, view: 1 } },
    );

    return {
      success:
        SHORT_LINK_STATUS.ACTIVATED === fileShortLinkMeta.shortLinkStatus,
      expirationLink: fileShortLink.expirationLink,
    };
  }

  async updateStatusShortLink(
    shortLinkId: string,
    shortLinkStatus: SHORT_LINK_STATUS,
  ): Promise<{
    success: boolean;
  }> {
    await this.fileShortLinkMetaModel.updateOne(
      { shortLinkId },
      { shortLinkStatus },
    );
    return {
      success: true,
    };
  }

  async addShortLinkUrl(
    shortLinkId: string,
    shortLinkUrl: string,
    authUser: User,
  ): Promise<FileShortLinkMetaResponseDTO> {
    try {
      const fileShortLinkMeta =
        await this.fileShortLinkMetaModel.findOneAndUpdate(
          {
            shortLinkId,
          },
          {
            shortLinkUrl,
            updatedAt: new Date(),
            updatedBy: authUser.telegramId,
          },
          {
            new: true,
          },
        );

      if (isNil(fileShortLinkMeta)) {
        throw new BadRequestException('Short link not found');
      }

      const fileShortLink = await this.fileShortLinkModel
        .findById(fileShortLinkMeta.fileShortLinkId)
        .lean();

      if (isNil(fileShortLink)) {
        throw new BadRequestException('File not found');
      }

      return plainToInstance(FileShortLinkMetaResponseDTO, fileShortLinkMeta, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      console.log('Error adding short link URL:', error);
      throw new BadRequestException(
        'Error adding short link URL',
        error.message,
      );
    }
  }

  async getDashboardOverview(filter: { startDate?: string; endDate?: string }) {
    const { startDate, endDate } = filter;
    const match: any = {};
    if (startDate || endDate) {
      match.createdAt = {};
      if (startDate) match.createdAt.$gte = new Date(startDate);
      if (endDate) match.createdAt.$lte = new Date(endDate);
    }

    const totalFiles = await this.fileShortLinkModel.countDocuments(match);

    const totalLinks = await this.fileShortLinkMetaModel.countDocuments(match);

    const availableLinks = await this.fileShortLinkMetaModel.countDocuments({
      ...match,
      shortLinkStatus: SHORT_LINK_STATUS.ACTIVATED,
    });

    const downloadedLinks = await this.fileShortLinkMetaModel.countDocuments({
      ...match,
      shortLinkStatus: SHORT_LINK_STATUS.DEACTIVATED,
    });

    const completionRate =
      totalLinks > 0 ? Math.round((downloadedLinks / totalLinks) * 100) : 0;

    const totalViewsAgg = await this.fileShortLinkMetaModel.aggregate([
      { $group: { _id: null, total: { $sum: '$views' } } },
    ]);
    const totalViews = totalViewsAgg[0]?.total || 0;

    const topBrandsAgg = await this.fileShortLinkModel.aggregate([
      { $match: match },
      {
        $lookup: {
          from: 'file_short_link_meta',
          localField: '_id',
          foreignField: 'fileShortLinkId',
          as: 'metas',
        },
      },
      // description: 'Group by brand and calculate totals'
      {
        $group: {
          _id: '$brand',
          files: { $sum: 1 },
          links: { $sum: { $size: '$metas' } },
          downloaded: {
            $sum: {
              $size: {
                $filter: {
                  input: '$metas',
                  as: 'meta',
                  cond: {
                    $eq: [
                      '$$meta.shortLinkStatus',
                      SHORT_LINK_STATUS.DEACTIVATED,
                    ],
                  },
                },
              },
            },
          },
        },
      },
      { $sort: { files: -1 } },
      { $limit: 5 },
    ]);

    const topBrands = topBrandsAgg.map((b: any) => ({
      brand: b._id,
      files: b.files,
      links: b.links,
      downloaded: b.downloaded,
      percent: totalLinks > 0 ? Math.round((b.links / totalLinks) * 100) : 0,
    }));

    const recentFilesRaw = await this.fileShortLinkModel
      .find(match)
      .sort({ createdAt: -1 })
      .limit(10)
      .lean();

    const recentFiles = await Promise.all(
      recentFilesRaw.map(async (file) => {
        const metas = await this.fileShortLinkMetaModel
          .find({ fileShortLinkId: file._id })
          .lean();
        const downloaded = metas.filter(
          (m) => m.shortLinkStatus === SHORT_LINK_STATUS.DEACTIVATED,
        ).length;
        return {
          fileId: file._id,
          brand: file.brand,
          createdAt: file.createdAt,
          createdBy: file.createdByUsername,
          links: metas.length,
          downloaded,
        };
      }),
    );

    const chartAgg = await this.fileShortLinkMetaModel.aggregate([
      { $match: match },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' },
          },
          links: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ]);
    const chart = chartAgg.map((c) => ({
      date: c._id,
      links: c.links,
    }));

    const filesCreated = await this.fileShortLinkModel.countDocuments(match);

    const linksCreated =
      await this.fileShortLinkMetaModel.countDocuments(match);

    const avgPerDay =
      chart.length > 0
        ? Math.round(chart.reduce((sum, c) => sum + c.links, 0) / chart.length)
        : 0;

    return {
      totalFiles,
      totalLinks,
      availableLinks,
      downloadedLinks,
      completionRate,
      totalViews,
      filesCreated,
      linksCreated,
      avgPerDay,
      topBrands,
      recentFiles,
      chart,
    };
  }
}
