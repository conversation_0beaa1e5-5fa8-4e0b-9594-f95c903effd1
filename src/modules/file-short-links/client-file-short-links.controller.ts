import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { isNil } from 'lodash';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { CountryCodeFileShortLinkGuard } from 'src/shared/guards/country-code-file-short-link.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { CheckStatusShortLinkDTO } from '../short-link/dto/short-link.dto';
import {
  CreateFileShortLinksDTO,
  GetFileShortLinkQueryDTO,
  GetFileShortLinksQueryDTO,
} from './dto/file-short-links.dto';
import { FileShortLinksService } from './file-short-links.service';

@ApiTags('Client File Short Links')
@Controller('client/file-short-links')
export class ClientFileShortLinksController {
  constructor(private readonly fileShortLinkService: FileShortLinksService) {}

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post('/create')
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'file', maxCount: 1 }, // Attachment file
      { name: 'logo', maxCount: 1 }, // Logo
    ]),
  )
  async createFileShortLink(
    @Body() body: CreateFileShortLinksDTO,
    @UploadedFiles()
    files: { file?: Express.Multer.File[]; logo?: Express.Multer.File[] },
  ) {
    const file = files.file?.[0];
    const logo = files.logo?.[0];
    if (isNil(file)) {
      throw new BadRequestException('File is required');
    }
    return this.fileShortLinkService.createFileShortLink({
      createFileShortLinkDTO: body,
      file,
      logo,
    });
  }

  @UseGuards(GetEmailGuard)
  @Get('/get-detail')
  async getDetailFileShortLink(@Query() query: GetFileShortLinkQueryDTO) {
    return this.fileShortLinkService.getFileShortLinkDetail(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('')
  async getFiles(@Query() query: GetFileShortLinksQueryDTO) {
    return this.fileShortLinkService.getFileShortLinks(query);
  }

  @Get(':id/download')
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } =
      await this.fileShortLinkService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'attachment');

    stream.pipe(res);
  }

  @UseGuards(GetEmailGuard, CountryCodeFileShortLinkGuard)
  @Get('check-status')
  async checkStatusShortLink(@Query() query: CheckStatusShortLinkDTO) {
    return this.fileShortLinkService.checkStatusShortLink(query.shortLinkId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Post(':shortLinkId/add-short-link-url')
  async addShortLinkUrl(
    @Param('shortLinkId') shortLinkId: string,
    @Body() body: { shortLinkUrl: string },
    @RequestUser() user: User,
  ) {
    return this.fileShortLinkService.addShortLinkUrl(
      shortLinkId,
      body.shortLinkUrl,
      user,
    );
  }
}
