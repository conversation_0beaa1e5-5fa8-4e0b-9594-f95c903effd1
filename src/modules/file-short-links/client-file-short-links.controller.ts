import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { Response } from 'express';
import { isNil } from 'lodash';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { CountryCodeFileShortLinkGuard } from 'src/shared/guards/country-code-file-short-link.guard';
import { GetEmailGuard } from 'src/shared/guards/get-email.guard';
import { CheckStatusShortLinkDTO } from '../short-link/dto/short-link.dto';
import {
  CreateFileShortLinksDTO,
  GetFileShortLinkQueryDTO,
  GetFileShortLinksQueryDTO,
} from './dto/file-short-links.dto';
import { FileShortLinksService } from './file-short-links.service';

@ApiTags('Client File Short Links')
@UseGuards(GetEmailGuard)
@Controller('client/file-short-links')
export class ClientFileShortLinksController {
  constructor(private readonly fileShortLinkService: FileShortLinksService) {}

  @Get('/get-detail')
  async getDetailFileShortLink(@Query() query: GetFileShortLinkQueryDTO) {
    return this.fileShortLinkService.getFileShortLinkDetail(query);
  }

  @ApiBearerAuth('JWT-auth')
  @Get('')
  async getFiles(@Query() query: GetFileShortLinksQueryDTO) {
    return this.fileShortLinkService.getFileShortLinks(query);
  }

  @Get(':id/download')
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } =
      await this.fileShortLinkService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'attachment');

    stream.pipe(res);
  }

  @UseGuards(CountryCodeFileShortLinkGuard)
  @Get('check-status')
  async checkStatusShortLink(@Query() query: CheckStatusShortLinkDTO) {
    return this.fileShortLinkService.checkStatusShortLink(query.shortLinkId);
  }
}
