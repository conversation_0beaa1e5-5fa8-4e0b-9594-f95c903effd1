import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdminDB } from 'src/config/admin-db.config';
import {
  GroupUserConnection,
  GroupUserConnectionSchema,
} from 'src/shared/entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenSchema,
} from 'src/shared/entities/auth/valid-token.entity';
import {
  JobApplicant,
  JobApplicantSchema,
} from 'src/shared/entities/job-applicant.entity';
import { Job, JobSchema } from 'src/shared/entities/job.entity';
import { Page, PageSchema } from 'src/shared/entities/page.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { AuthModule } from '../auth/auth.module';
import { ApplicantsController } from './applicants.controller';
import { ApplicantsService } from './applicants.service';

@Module({
  imports: [
    MongooseModule.forFeature(
      [
        {
          name: JobApplicant.name,
          schema: JobApplicantSchema,
        },
        {
          name: Job.name,
          schema: JobSchema,
        },
        {
          name: ValidToken.name,
          schema: ValidTokenSchema,
        },
        {
          name: GroupUserConnection.name,
          schema: GroupUserConnectionSchema,
        },
        {
          name: Page.name,
          schema: PageSchema,
        },
      ],
      AdminDB,
    ),
    AuthModule,
  ],
  controllers: [ApplicantsController],
  providers: [ApplicantsService, AzureStorageService],
  exports: [ApplicantsService],
})
export class ApplicantsModule {}
