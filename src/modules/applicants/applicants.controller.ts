import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { Response } from 'express';
import { RequestUser } from 'src/shared/decorators/request-user.decorator';
import { JobApplicant } from 'src/shared/entities/job-applicant.entity';
import { User } from 'src/shared/entities/auth/user.entity';
import { AuthGuard } from 'src/shared/guards/auth.guard';
import { ApplicantsService } from './applicants.service';
import { CreateApplicantDTO } from './dto/apply.dto';
import {
  ApplicantResponseDTO,
  GetApplicantsQueryDTO,
  GetApplicantsResponseDTO,
} from './dto/get-applicants.dto';
import { fileFilter } from 'src/shared/utils/file-filter.util';
import {
  ClientInfo,
  ClientInfoData,
} from 'src/shared/decorators/client-info.decorator';
import { Roles } from 'src/shared/decorators/role.decorator';
import { RolesGuard } from 'src/shared/guards/role.guard';
import { ROLE } from 'src/shared/constants/auth.constant';

@ApiTags('Applicants')
@Controller('applicants')
export class ApplicantsController {
  constructor(private readonly applicantsService: ApplicantsService) {}

  @Post('apply')
  @ApiOperation({
    summary: 'Submit job application',
    description: 'Submit a job application with CV/resume file upload',
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Job application data with CV file',
    schema: {
      type: 'object',
      properties: {
        fullName: {
          type: 'string',
          description: 'Applicant full name',
          example: 'John Doe',
        },
        email: {
          type: 'string',
          description: 'Applicant email address',
          example: '<EMAIL>',
        },
        phone: {
          type: 'string',
          description: 'Applicant phone number',
          example: '+1234567890',
        },
        jobId: {
          type: 'string',
          description: 'Job posting ID',
          example: '507f1f77bcf86cd799439011',
        },
        file: {
          type: 'string',
          format: 'binary',
          description: 'CV/Resume file (PDF, DOC, DOCX)',
        },
      },
      required: ['fullName', 'email', 'jobId', 'file'],
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Application submitted successfully',
    type: JobApplicant,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid input data or file format',
  })
  @UseInterceptors(
    FileInterceptor('file', {
      fileFilter,
      // limits: { fileSize: 5 * 1024 * 1024 }, // 5 MB
    }),
  )
  async apply(
    @Body() createApplicantDTO: CreateApplicantDTO,
    @UploadedFile() file: Express.Multer.File,
    @ClientInfo() clientInfo: ClientInfoData,
  ): Promise<JobApplicant> {
    return this.applicantsService.createApplicant(
      createApplicantDTO,
      file,
      clientInfo,
    );
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('')
  @ApiOperation({
    summary: 'Get job applicants',
    description:
      'Retrieve a paginated list of job applicants with optional filtering',
  })
  @ApiResponse({
    status: 200,
    description: 'Applicants retrieved successfully',
    type: GetApplicantsResponseDTO,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getApplicants(
    @Query() query: GetApplicantsQueryDTO,
  ): Promise<GetApplicantsResponseDTO> {
    return this.applicantsService.getApplicants(query);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard)
  @Get('detail/:applicantId')
  @ApiOperation({
    summary: 'Get applicant detail',
    description: 'Retrieve detailed information about a specific job applicant',
  })
  @ApiParam({
    name: 'applicantId',
    description: 'Applicant ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Applicant retrieved successfully',
    type: ApplicantResponseDTO,
  })
  @ApiResponse({
    status: 400,
    description: 'Applicant not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  async getDetailApplicant(
    @Param('applicantId') applicantId: string,
  ): Promise<ApplicantResponseDTO> {
    return this.applicantsService.getApplicantById(applicantId);
  }

  @ApiBearerAuth('JWT-auth')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(ROLE.ADMIN, ROLE.SUPER_ADMIN)
  @Delete(':applicantId')
  @ApiOperation({
    summary: 'Delete job applicant',
    description: 'Soft delete a job applicant (Admin/Super Admin only)',
  })
  @ApiParam({
    name: 'applicantId',
    description: 'Applicant ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'Applicant deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true,
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Applicant not found',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Admin role required',
  })
  async deleteApplicant(
    @Param('applicantId') applicantId: string,
    @RequestUser() user: User,
  ): Promise<{
    success: boolean;
  }> {
    await this.applicantsService.deleteApplicant(applicantId, user);
    return { success: true };
  }

  @Get(':id/download')
  @ApiOperation({
    summary: 'Download applicant CV/resume',
    description: 'Download the CV/resume file submitted by the applicant',
  })
  @ApiParam({
    name: 'id',
    description: 'Applicant ID',
    example: '507f1f77bcf86cd799439011',
  })
  @ApiResponse({
    status: 200,
    description: 'File downloaded successfully',
    content: {
      'application/octet-stream': {
        schema: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Applicant not found or file not available',
  })
  async downloadFile(
    @Param('id') id: string,
    @Res() res: Response,
  ): Promise<void> {
    const { stream, contentType } =
      await this.applicantsService.downloadFile(id);

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', 'attachment');

    stream.pipe(res);
  }
}
