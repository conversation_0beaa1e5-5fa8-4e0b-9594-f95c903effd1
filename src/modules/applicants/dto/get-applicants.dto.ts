import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsOptional, IsString, IsNumber, Min, Max } from 'class-validator';
import { ClientInfoData } from 'src/shared/decorators/client-info.decorator';
import { JobResponseDTO } from 'src/modules/jobs/dto/get-jobs.dto';
import { PageResponseDTO } from 'src/modules/pages/dto/get-pages.dto';

export class GetApplicantsQueryDTO {
  @ApiPropertyOptional({
    description: 'Number of records to skip for pagination',
    example: 0,
    default: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  skip?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of records to return',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Filter by email address',
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({
    description: 'Filter by full name',
    example: 'John Doe',
  })
  @IsString()
  @IsOptional()
  fullName?: string;

  @ApiPropertyOptional({
    description: 'Filter by phone number',
    example: '+1234567890',
  })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Filter by creator ID',
    example: '123456789',
  })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiPropertyOptional({
    description: 'Filter by page ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  pageId?: string;

  @ApiPropertyOptional({
    description: 'Filter by page code',
    example: 'careers-page',
  })
  @IsString()
  @IsOptional()
  pageCode?: string;

  @ApiPropertyOptional({
    description: 'Filter by job ID',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  @IsOptional()
  jobId?: string;

  @ApiPropertyOptional({
    description: 'Filter by start date',
    example: '2023-01-01',
  })
  @IsString()
  @IsOptional()
  startedDate?: string;

  @ApiPropertyOptional({
    description: 'Filter by end date',
    example: '2023-12-31',
  })
  @IsString()
  @IsOptional()
  endedDate?: string;

  @ApiPropertyOptional({
    description: 'Filter by tag',
    example: 'urgent',
  })
  @IsString()
  @IsOptional()
  tag?: string;
}

export class ApplicantResponseDTO {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  _id: string;

  @Expose()
  @Type(() => JobResponseDTO)
  job: JobResponseDTO;

  @Expose()
  @Type(() => PageResponseDTO)
  page: PageResponseDTO;

  @Expose()
  clientInfo: ClientInfoData;

  @Expose()
  fullName: string;

  @Expose()
  email: string;

  @Expose()
  phone: string;

  @Expose()
  resume: string;

  @Expose()
  secondaryPhone?: string;

  @Expose()
  address: string;

  @Expose()
  tag?: string;

  @Expose()
  createdAt: Date;

  @Expose()
  updatedAt?: Date;
}

export class GetApplicantsResponseDTO {
  @ApiProperty({
    description: 'Array of applicants',
    type: [ApplicantResponseDTO],
  })
  @Expose()
  data: Array<ApplicantResponseDTO>;

  @ApiProperty({
    description: 'Total number of applicants matching the query',
    example: 50,
  })
  @Expose()
  total: number;

  @ApiProperty({
    description: 'Number of records skipped',
    example: 0,
  })
  @Expose()
  skip: number;

  @ApiProperty({
    description: 'Number of records returned',
    example: 10,
  })
  @Expose()
  limit: number;
}
