import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { plainToClass } from 'class-transformer';
import { isNil } from 'lodash';
import { Model, Types } from 'mongoose';
import { AdminDB } from 'src/config';
import {
  ClientInfo,
  ClientInfoData,
} from 'src/shared/decorators/client-info.decorator';
import { User } from 'src/shared/entities/auth/user.entity';
import {
  JobApplicant,
  JobApplicantDocument,
} from 'src/shared/entities/job-applicant.entity';
import { Job, JobDocument } from 'src/shared/entities/job.entity';
import { Page, PageDocument } from 'src/shared/entities/page.entity';
import { AzureStorageService } from 'src/shared/services/azure-storage.service';
import { parseDate } from 'src/shared/utils/common.util';
import { AuthModel } from 'src/shared/utils/middleware-entity.util';
import { CreateApplicantDTO } from './dto/apply.dto';
import {
  ApplicantResponseDTO,
  GetApplicantsQueryDTO,
  GetApplicantsResponseDTO,
} from './dto/get-applicants.dto';

@Injectable()
export class ApplicantsService {
  constructor(
    @InjectModel(Job.name, AdminDB)
    private jobModel: Model<JobDocument>,
    @InjectModel(JobApplicant.name, AdminDB)
    private jobApplicantModel: AuthModel<JobApplicantDocument>,
    @InjectModel(Page.name, AdminDB)
    private pageModel: Model<PageDocument>,

    private readonly azureStorageService: AzureStorageService,
  ) {}

  async createApplicant(
    createApplicantDTO: CreateApplicantDTO,
    file: Express.Multer.File,
    @ClientInfo() clientInfo: ClientInfoData,
  ): Promise<JobApplicant> {
    try {
      const job = await this.jobModel.findById(createApplicantDTO.jobId);
      if (isNil(job)) {
        throw new BadRequestException('Job not found');
      }
      const page = await this.pageModel.findOne({
        code: createApplicantDTO.pageCode,
      });
      if (isNil(page)) {
        throw new BadRequestException('Page not found');
      }

      // Upload file to Azure Storage
      const url = await this.azureStorageService.uploadFile(file);

      return this.jobApplicantModel.create({
        ...createApplicantDTO,
        pageId: page._id,
        tag: createApplicantDTO.tag,
        clientInfo,
        resume: url,
        createdAt: new Date(),
        createdBy: createApplicantDTO.email,
      });
    } catch (error) {
      console.log(error);
      throw new BadRequestException(
        'Failed to create applicant',
        error.message,
      );
    }
  }

  async getApplicants(
    query: GetApplicantsQueryDTO,
  ): Promise<GetApplicantsResponseDTO> {
    const {
      skip = 0,
      limit = 10,
      email,
      fullName,
      phone,
      pageId,
      pageCode,
      jobId,
      startedDate,
      endedDate,
      tag,
    } = query;
    const filter = {};

    if (email) {
      filter['email'] = email;
    }
    if (fullName) {
      filter['fullName'] = {
        $regex: new RegExp(fullName, 'i'),
      };
    }
    if (phone) {
      filter['phone'] = phone;
    }
    if (pageCode) {
      const page = await this.pageModel.findOne({ code: pageCode });
      if (isNil(page)) {
        throw new BadRequestException('Page not found');
      }
      filter['pageId'] = page._id;
    } else if (pageId) {
      filter['pageId'] = new Types.ObjectId(pageId);
    }
    if (jobId) {
      const job = await this.jobModel.findById(jobId);
      if (isNil(job)) {
        throw new BadRequestException('Job not found');
      }
      filter['jobId'] = String(job._id);
    }
    if (startedDate) {
      const startDate = parseDate(startedDate);
      if (!startDate) {
        throw new BadRequestException(
          'Invalid startedAt format. Expected dd-mm-yyyy.',
        );
      }
      filter['createdAt'] = { $gte: startDate };
    }
    if (endedDate) {
      const endDate = parseDate(endedDate);
      if (!endDate) {
        throw new BadRequestException(
          'Invalid endedAt format. Expected dd-mm-yyyy.',
        );
      }
      filter['createdAt'] = {
        ...(filter['createdAt'] || {}),
        $lte: endDate,
      };
    }
    if (tag) {
      filter['tag'] = {
        $regex: new RegExp(tag, 'i'),
      };
    }

    const [data, total] = await Promise.all([
      this.jobApplicantModel
        .aggregate([
          { $match: filter },
          {
            $lookup: {
              from: 'jobs',
              let: { jobId: { $toObjectId: '$jobId' } },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$_id', '$$jobId'] },
                  },
                },
              ],
              as: 'job',
            },
          },
          {
            $unwind: {
              path: '$job',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $lookup: {
              from: 'pages',
              let: { pageId: { $toObjectId: '$pageId' } },
              pipeline: [
                {
                  $match: {
                    $expr: { $eq: ['$_id', '$$pageId'] },
                  },
                },
              ],
              as: 'page',
            },
          },
          {
            $unwind: {
              path: '$page',
              preserveNullAndEmptyArrays: true,
            },
          },
          { $sort: { createdAt: -1 } },
          { $skip: skip },
          { $limit: limit },
        ])
        .exec(),
      this.jobApplicantModel.countDocuments(filter),
    ]);

    return {
      data: data.map((applicant) =>
        plainToClass(ApplicantResponseDTO, applicant, {
          excludeExtraneousValues: true,
        }),
      ),
      total,
      skip,
      limit,
    };
  }

  async getApplicantById(applicantId: string): Promise<ApplicantResponseDTO> {
    const applicant = await this.jobApplicantModel.aggregate([
      { $match: { _id: new Types.ObjectId(applicantId) } },
      {
        $lookup: {
          from: 'jobs',
          let: { jobId: { $toObjectId: '$jobId' } },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$_id', '$$jobId'] },
              },
            },
          ],
          as: 'job',
        },
      },
      {
        $unwind: {
          path: '$job',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'pages',
          let: { pageId: { $toObjectId: '$pageId' } },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ['$_id', '$$pageId'] },
              },
            },
          ],
          as: 'page',
        },
      },
      {
        $unwind: {
          path: '$page',
          preserveNullAndEmptyArrays: true,
        },
      },
    ]);
    if (isNil(applicant)) {
      throw new BadRequestException('Applicant not found');
    }
    return plainToClass(ApplicantResponseDTO, applicant[0], {
      excludeExtraneousValues: true,
    });
  }

  async deleteApplicant(applicantId: string, authUser: User): Promise<void> {
    try {
      const applicant = await this.jobApplicantModel.findOneAndUpdate(
        { _id: applicantId },
        { deletedAt: new Date(), deletedBy: authUser.telegramId },
      );
      if (isNil(applicant)) {
        throw new BadRequestException('Applicant not found');
      }
    } catch (error) {
      console.log('error', error);
      throw new BadRequestException('Error delete applicant', error.message);
    }
  }

  async downloadFile(
    applicantId: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const applicant = await this.jobApplicantModel.findById(applicantId);
      if (isNil(applicant)) {
        throw new BadRequestException('Applicant not found');
      }

      if (!applicant.resume) {
        throw new BadRequestException('No file found for this applicant');
      }

      return this.azureStorageService.downloadFile(applicant.resume);
    } catch (error) {
      console.log('error downloading applicants', error);
      throw new BadRequestException('Failed to download file', error.message);
    }
  }
}
