import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../utils/middleware-entity.util';

export type DomainDocument = Domain & Document;

@Schema({
  collection: 'domains',
})
export class Domain {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: string;
}

export const DomainSchema = SchemaFactory.createForClass(Domain);

addAuthStaticMethods(DomainSchema);

// Add a pre-query middleware to filter out soft-deleted documents
DomainSchema.pre<Query<DomainDocument, DomainDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
DomainSchema.pre<Query<DomainDocument, DomainDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

DomainSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
