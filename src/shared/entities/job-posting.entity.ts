import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';

export type JobPostingDocument = JobPosting & Document;

@Schema({ collection: 'job_postings' })
export class JobPosting {
  @Prop({ required: true })
  brand: string;

  @Prop({ required: true })
  job: string;

  @Prop({
    type: String,
  
  })
  file: string;
}

export const JobPostingSchema = SchemaFactory.createForClass(JobPosting);
