import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { PAGE_STATUS } from '../constants/page.constant';
import { addAuthStaticMethods } from '../utils/middleware-entity.util';

export type PageDocument = Page & Document;

@Schema({
  collection: 'pages',
})
export class Page {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  code: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Domain' })
  domainId: Types.ObjectId;

  @Prop({ required: false, default: PAGE_STATUS.ACTIVATED })
  status: PAGE_STATUS;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: string;
}

export const PageSchema = SchemaFactory.createForClass(Page);

addAuthStaticMethods(PageSchema);

// Add a pre-query middleware to filter out soft-deleted documents
PageSchema.pre<Query<PageDocument, PageDocument>>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
PageSchema.pre<Query<PageDocument, PageDocument>>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

PageSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
