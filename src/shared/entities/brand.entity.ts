import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../utils/middleware-entity.util';

export type BrandDocument = Brand & Document;

@Schema({
  collection: 'brands',
})
export class Brand {
  @Prop({ required: true, unique: true })
  brand: string;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Number })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;
}

export const BrandSchema = SchemaFactory.createForClass(Brand);

addAuthStaticMethods(BrandSchema);

// Add a pre-query middleware to filter out soft-deleted documents
BrandSchema.pre<Query<BrandDocument, BrandDocument>>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
BrandSchema.pre<Query<BrandDocument, BrandDocument>>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

BrandSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
