import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';

export type FileEmailDocument = FileEmail & Document;

@Schema({
  collection: 'file_emails',
})
export class FileEmail {
  @Prop({ required: true })
  email: string;

  @Prop({ required: false })
  shortLinkId: string;

  @Prop({ required: false, enum: SHORT_LINK_STATUS })
  shortLinkStatus: SHORT_LINK_STATUS;

  @Prop({ required: false })
  shortLinkUrl: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'File' })
  fileId: Types.ObjectId;

  @Prop({ required: true })
  domainName: string;

  @Prop({ required: false, default: false })
  popup: boolean;

  @Prop({ required: false })
  expirationLink?: string;

  @Prop({ required: false, default: '' })
  countryCode: string;

  @Prop({ required: false, default: 0 })
  countCheck: number;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const FileEmailSchema = SchemaFactory.createForClass(FileEmail);

// Add a pre-query middleware to filter out soft-deleted documents
FileEmailSchema.pre<Query<FileEmailDocument, FileEmailDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

FileEmailSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
