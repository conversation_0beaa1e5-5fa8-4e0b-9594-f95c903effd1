import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { SHORT_LINK_STATUS } from 'src/shared/constants/short-link.constant';

export type FileShortLinkMetaDocument = FileShortLinkMeta & Document;

@Schema({
  collection: 'file_short_link_meta',
})
export class FileShortLinkMeta {
  @Prop({ required: false })
  shortLinkId: string;

  @Prop({ required: false, enum: SHORT_LINK_STATUS })
  shortLinkStatus: SHORT_LINK_STATUS;

  @Prop({ required: true, type: Types.ObjectId, ref: 'FileShortLink' })
  fileShortLinkId: Types.ObjectId;

  @Prop({ required: false, default: false })
  popup: boolean;

  @Prop({ required: false, default: '' })
  countryCode: string;

  @Prop({ required: false })
  shortLinkUrl?: string;

  @Prop({ required: false, default: 0 })
  views: number;

  @Prop({ required: false, default: 0 })
  countCheck: number;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const FileShortLinkMetaSchema =
  SchemaFactory.createForClass(FileShortLinkMeta);

// Add a pre-query middleware to filter out soft-deleted documents
FileShortLinkMetaSchema.pre<
  Query<FileShortLinkMetaDocument, FileShortLinkMetaDocument>
>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
FileShortLinkMetaSchema.pre<
  Query<FileShortLinkMetaDocument, FileShortLinkMetaDocument>
>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

FileShortLinkMetaSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
