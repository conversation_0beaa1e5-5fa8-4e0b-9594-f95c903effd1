import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';

export type WebhookDocument = Webhook & Document;

@Schema({
  collection: 'webhooks',
})
export class Webhook {
  @Prop({ required: true })
  webhookUrl: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: String })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ type: Date })
  updatedAt: Date;

  @Prop({ type: String })
  updatedBy: number;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: String })
  deletedBy?: string;
}

export const WebhookSchema = SchemaFactory.createForClass(Webhook);

addAuthStaticMethods(WebhookSchema);

// Add indexes for better performance
WebhookSchema.index(
  { webhookUrl: 1 },
  {
    unique: true,
    name: 'webhookUrl_1_unique',
    background: true,
  },
);

WebhookSchema.index(
  { name: 1 },
  {
    name: 'name_1',
    background: true,
  },
);

WebhookSchema.index(
  { createdAt: -1 },
  {
    name: 'createdAt_-1',
    background: true,
  },
);

WebhookSchema.index(
  { deletedAt: 1 },
  {
    name: 'deletedAt_1',
    background: true,
    sparse: true, // Only index documents that have deletedAt field
  },
);

// Compound index for common queries (active webhooks sorted by creation date)
WebhookSchema.index(
  { deletedAt: 1, createdAt: -1 },
  {
    name: 'deletedAt_1_createdAt_-1',
    background: true,
  },
);

// Add a pre-query middleware to filter out soft-deleted documents
WebhookSchema.pre<Query<WebhookDocument, WebhookDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
WebhookSchema.pre<Query<WebhookDocument, WebhookDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

WebhookSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
