import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { ClientInfoData } from '../decorators/client-info.decorator';
import { addAuthStaticMethods } from '../utils/middleware-entity.util';

export type JobApplicantDocument = JobApplicant & Document;

@Schema({
  collection: 'job_applicants',
})
export class JobApplicant {
  @Prop({ required: true, type: Types.ObjectId, ref: 'Job' })
  jobId: Types.ObjectId;

  @Prop({ required: true })
  fullName: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  phone: string;

  @Prop({ required: false })
  secondaryPhone: string;

  @Prop({ required: true })
  address: string;

  @Prop({ required: true })
  resume: string;

  @Prop({ required: false })
  status: string;

  @Prop({ required: false, type: Types.ObjectId, ref: 'Page' })
  pageId: Types.ObjectId;

  @Prop({
    required: false,
    type: Object,
  })
  clientInfo: ClientInfoData;

  @Prop({ required: false })
  tag: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const JobApplicantSchema = SchemaFactory.createForClass(JobApplicant);

addAuthStaticMethods(JobApplicantSchema);

// Add a pre-query middleware to filter out soft-deleted documents
JobApplicantSchema.pre<Query<JobApplicantDocument, JobApplicantDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
JobApplicantSchema.pre<Query<JobApplicantDocument, JobApplicantDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

JobApplicantSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
