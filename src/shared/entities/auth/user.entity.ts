import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { ROLE } from 'src/shared/constants/auth.constant';

export type UserDocument = User & Document;

@Schema({
  collection: 'users',
  timestamps: true,
})
export class User {
  @Prop({ unique: true, sparse: true })
  telegramId: number;

  @Prop()
  firstName: string;

  @Prop()
  lastName: string;

  @Prop({ unique: true, required: true })
  username: string;

  @Prop({ required: false })
  password: string;

  @Prop({ required: false })
  group: string;

  @Prop()
  languageCode: string;

  @Prop({
    type: String,
    enum: Object.values(ROLE),
    default: ROLE.USER,
  })
  role: ROLE;

  @Prop({ default: 0, required: false })
  credit: number;
}

export const UserSchema = SchemaFactory.createForClass(User);

UserSchema.index(
  { id: 1 },
  { unique: true, partialFilterExpression: { id: { $ne: 0 } } },
);
