import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserWhitelistDocument = UserWhitelist & Document;

@Schema({
  collection: 'user_whitelists',
})
export class UserWhitelist {
  @Prop({ unique: true, required: true })
  username: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;
}

export const UserWhitelistSchema = SchemaFactory.createForClass(UserWhitelist);

UserWhitelistSchema.index(
  { id: 1 },
  { unique: true, partialFilterExpression: { id: { $ne: 0 } } },
);
