import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ValidTokenDocument = ValidToken & Document;

@Schema({
  collection: 'valid_tokens',
  timestamps: true,
})
export class ValidToken {
  @Prop({ required: false })
  accessToken: string;

  @Prop({ required: false })
  refreshToken: string;

  @Prop({
    required: true,
    default: () => new Date(),
    expires: 14 * 24 * 60 * 60,
  })
  expiresAt: Date; // MongoDB will automatically delete documents after this date
}

export const ValidTokenSchema = SchemaFactory.createForClass(ValidToken);
