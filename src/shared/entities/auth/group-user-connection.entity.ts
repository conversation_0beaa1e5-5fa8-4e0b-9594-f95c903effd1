import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type GroupUserConnectionDocument = GroupUserConnection & Document;

@Schema({
  collection: 'group_user_connections',
})
export class GroupUserConnection {
  @Prop({ required: true, type: Types.ObjectId, ref: 'Group' })
  groupId: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId, ref: 'User' })
  userId: Types.ObjectId;
}

export const GroupUserConnectionSchema =
  SchemaFactory.createForClass(GroupUserConnection);
