import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TokenDocument = Token & Document;

@Schema({
  collection: 'tokens',
  timestamps: true,
})
export class Token {
  @Prop({ required: true, unique: true })
  token: string;

  @Prop({ required: true })
  telegramId: string;

  @Prop({ required: true })
  userName: string;

  @Prop({
    required: true,
    expires: 14 * 24 * 60 * 60, // TTL index: 14 days
  })
  expiresAt: Date;
}

export const TokenSchema = SchemaFactory.createForClass(Token);
