import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';
import { CAMP_USER_STATUS } from '../../constants/camp.constant';
import { EmailTracking } from './email-tracking.entity';

export type CampUserDocument = CampUser & Document;

@Schema({
  collection: 'camp_users',
  timestamps: true,
})
export class CampUser {
  @Prop({ required: true, type: String })
  userId: string;

  @Prop({ required: true, type: Types.ObjectId, ref: 'Camp' })
  campId: Types.ObjectId;

  @Prop({ required: true })
  email: string;

  @Prop({ type: String })
  firstName?: string;

  @Prop({ type: String })
  lastName?: string;

  @Prop({
    required: true,
    enum: CAMP_USER_STATUS,
    default: CAMP_USER_STATUS.ACTIVE,
  })
  status: CAMP_USER_STATUS;

  @Prop({ type: [EmailTracking], default: [] })
  emailTracking: EmailTracking[];

  @Prop({ type: Date })
  enrolledAt: Date;

  @Prop({ type: Date })
  lastEmailSentAt?: Date;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Date })
  unsubscribedAt?: Date;

  @Prop({ type: String })
  unsubscribeReason?: string;

  @Prop({ type: Object })
  metadata?: Record<string, any>;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Number })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ type: Date })
  updatedAt?: Date;

  @Prop({ type: Number })
  updatedBy?: number;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: Number })
  deletedBy?: number;
}

export const CampUserSchema = SchemaFactory.createForClass(CampUser);

addAuthStaticMethods(CampUserSchema);

// Add a pre-query middleware to filter out soft-deleted documents
CampUserSchema.pre<Query<CampUserDocument, CampUserDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
CampUserSchema.pre<Query<CampUserDocument, CampUserDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

CampUserSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
