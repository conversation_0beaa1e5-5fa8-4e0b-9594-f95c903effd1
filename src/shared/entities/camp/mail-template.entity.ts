import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';

export type MailTemplateDocument = MailTemplate & Document;

export class MailHTML {
  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  content: string;

  @Prop({ default: 0 })
  delay?: number;
}

@Schema({
  collection: 'mail_templates',
})
export class MailTemplate {
  @Prop({ required: true })
  sender: string;

  @Prop({ required: true })
  senderName: string;

  @Prop({ type: MailHTML })
  startHTML: MailHTML;

  @Prop({ type: [MailHTML], default: [] })
  otherHTML: MailHTML[];

  @Prop({ required: false })
  brand: string;

  @Prop({ type: MailHTML })
  endHTML: MailHTML;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: number;
}

export const MailTemplateSchema = SchemaFactory.createForClass(MailTemplate);

addAuthStaticMethods(MailTemplateSchema);

// Add a pre-query middleware to filter out soft-deleted documents
MailTemplateSchema.pre<Query<MailTemplateDocument, MailTemplateDocument>>(
  /^find/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
    next();
  },
);

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
MailTemplateSchema.pre<Query<MailTemplateDocument, MailTemplateDocument>>(
  /^count/,
  function (next) {
    this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
    next();
  },
);

MailTemplateSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
