import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { addAuthStaticMethods } from '../../utils/middleware-entity.util';

export type CampDocument = Camp & Document;

@Schema({
  collection: 'camps',
})
export class Camp {
  @Prop({ required: true })
  brand: string;

  @Prop({ type: Types.ObjectId, ref: 'Webhook', required: true })
  webhook: Types.ObjectId;

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  file: string;

  @Prop({ required: false, ref: 'MailTemplate' })
  mail: string;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: String })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ type: Date })
  updatedAt: Date;

  @Prop({ type: String })
  updatedBy: number;

  @Prop({ type: Date })
  deletedAt?: Date;

  @Prop({ type: String })
  deletedBy?: string;
}

export const CampSchema = SchemaFactory.createForClass(Camp);

addAuthStaticMethods(CampSchema);

// Add a pre-query middleware to filter out soft-deleted documents
CampSchema.pre<Query<CampDocument, CampDocument>>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
CampSchema.pre<Query<CampDocument, CampDocument>>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

CampSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
