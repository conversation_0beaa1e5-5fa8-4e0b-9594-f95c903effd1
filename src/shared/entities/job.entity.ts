import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Query, Types } from 'mongoose';
import { JOB_STATUS } from '../constants/job.constant';
import { addAuthStaticMethods } from '../utils/middleware-entity.util';

export type JobDocument = Job & Document;

@Schema({
  collection: 'jobs',
})
export class Job {
  @Prop({ required: true })
  title: string;

  @Prop({ required: false })
  description: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  address: string;

  @Prop({ required: false, type: Types.ObjectId, ref: 'Page' })
  pageId: Types.ObjectId;

  @Prop({ required: false })
  tags: string[];

  @Prop({ required: false, default: JOB_STATUS.ACTIVATED })
  status: JOB_STATUS;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  createdBy: number;

  @Prop({ type: String, required: false })
  createdByUsername?: string;

  @Prop({ required: false })
  updatedAt?: Date;

  @Prop({ required: false })
  updatedBy?: number;

  @Prop({ required: false })
  deletedAt?: Date;

  @Prop({ required: false })
  deletedBy?: string;
}

export const JobSchema = SchemaFactory.createForClass(Job);

addAuthStaticMethods(JobSchema);

// Add a pre-query middleware to filter out soft-deleted documents
JobSchema.pre<Query<JobDocument, JobDocument>>(/^find/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `find` queries
  next();
});

// Add a pre-query middleware to filter out soft-deleted documents for `countDocuments`
JobSchema.pre<Query<JobDocument, JobDocument>>(/^count/, function (next) {
  this.where({ deletedAt: null }); // Automatically add `deletedAt: null` to all `count` queries
  next();
});

JobSchema.pre('aggregate', function (next) {
  const pipeline = this.pipeline();

  // Ensure the `$match` stage includes `deletedAt: null`
  const matchStage = { deletedAt: null };

  // Check if a `$match` stage already exists in the pipeline
  const existingMatchIndex = pipeline.findIndex(
    (stage) => (stage as any).$match,
  );

  if (existingMatchIndex >= 0) {
    // Merge the `deletedAt: null` condition with the existing `$match` stage
    (pipeline[existingMatchIndex] as any).$match = {
      ...(pipeline[existingMatchIndex] as any).$match,
      ...matchStage,
    };
  } else {
    // Add a new `$match` stage at the beginning of the pipeline
    pipeline.unshift({ $match: matchStage });
  }

  next();
});
