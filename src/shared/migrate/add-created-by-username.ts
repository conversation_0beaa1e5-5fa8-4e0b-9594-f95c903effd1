/**
 * Migration script to add createdByUsername field to all collections
 * Based on update-created-by.ts pattern for proper database connection
 */

import mongoose from 'mongoose';

// Configuration - sử dụng cùng pattern như update-created-by.ts
const DB_NAME = process.env.MONGO_ADMIN_DB_NAME || 'admin0x1';
let MONGO_URI = process.env.MONGO_URI || '';
if (!MONGO_URI.endsWith(`/${DB_NAME}`)) {
  MONGO_URI = `${MONGO_URI.replace(/\/$/, '')}/${DB_NAME}`;
}

// Collections that have createdBy field - only existing collections
const COLLECTIONS_WITH_CREATED_BY: string[] = [
  'brands',
  'camps',
  'camp_users',
  'domains',
  'files',
  'file_emails',
  'file_short_links', // ✅ Correct name from entity
  'file_short_link_meta', // ✅ Correct name from entity
  'groups',
  'jobs',
  'job_applicants',
  // 'login_attempts',       // ❌ Collection does not exist
  'mail_templates',
  'pages',
  'webhooks',
  'user_whitelists',
];

interface UserMapping {
  _id: any;
  telegramId: number;
  username: string;
}

interface DocumentWithCreatedBy {
  _id: any;
  createdBy: number;
  createdByUsername?: string;
}

export async function addCreatedByUsername(): Promise<void> {
  try {
    // Connect to MongoDB using mongoose
    console.log(`🔗 Connecting to: ${MONGO_URI}`);
    await mongoose.connect(MONGO_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('Database connection not established');
    }

    // List all collections to verify connection
    const collections = await db.listCollections().toArray();
    console.log(`📋 Found ${collections.length} collections in database`);
    console.log(`📋 Collections: ${collections.map((c) => c.name).join(', ')}`);

    // Step 1: Get user mapping (telegramId -> username)
    console.log('\n📊 Building user mapping...');
    const usersCollection = db.collection('users');

    // Check if users collection exists
    const userCollectionExists = collections.some((c) => c.name === 'users');
    if (!userCollectionExists) {
      console.log('⚠️  Users collection not found, creating sample mapping...');
      // Create a default mapping for system user
      const telegramIdToUsername = new Map<number, string>();
      telegramIdToUsername.set(0, 'system');
      console.log('📋 Created default mapping for system user');
    } else {
      const users: UserMapping[] = (await usersCollection
        .find({}, { projection: { telegramId: 1, username: 1 } })
        .toArray()) as UserMapping[];

      const telegramIdToUsername = new Map<number, string>();
      users.forEach((user: UserMapping) => {
        if (user.telegramId && user.username) {
          telegramIdToUsername.set(user.telegramId, user.username);
        }
      });

      console.log(
        `📋 Found ${telegramIdToUsername.size} users with telegramId and username`,
      );
    }

    // Step 2: Process each collection
    let totalUpdated = 0;
    const existingCollections = collections.map((c) => c.name);

    for (const collectionName of COLLECTIONS_WITH_CREATED_BY) {
      console.log(`\n🔧 Processing collection: ${collectionName}`);

      // Check if collection exists
      if (!existingCollections.includes(collectionName)) {
        console.log(
          `⚠️  Collection ${collectionName} does not exist, skipping...`,
        );
        continue;
      }

      const collection = db.collection(collectionName);

      // Get documents that have createdBy but no createdByUsername
      const documents: DocumentWithCreatedBy[] = (await collection
        .find({
          createdBy: { $exists: true },
          createdByUsername: { $exists: false },
        })
        .toArray()) as DocumentWithCreatedBy[];

      console.log(`   📄 Found ${documents.length} documents to update`);

      let updatedInCollection = 0;

      for (const doc of documents) {
        const createdBy: number = doc.createdBy;

        // Get username from mapping or use default
        let username: string;
        if (typeof createdBy === 'string') {
          // If createdBy is already a string (like email), use it as username
          username = createdBy;
        } else {
          // For telegramId, try to get username from mapping
          const telegramIdToUsername = new Map<number, string>();
          // Re-fetch users for each collection to ensure fresh data
          if (userCollectionExists) {
            const users: UserMapping[] = (await usersCollection
              .find({}, { projection: { telegramId: 1, username: 1 } })
              .toArray()) as UserMapping[];

            users.forEach((user: UserMapping) => {
              if (user.telegramId && user.username) {
                telegramIdToUsername.set(user.telegramId, user.username);
              }
            });
          }

          username = telegramIdToUsername.get(createdBy) || 'unknown';
        }

        await collection.updateOne(
          { _id: doc._id },
          { $set: { createdByUsername: username } },
        );
        updatedInCollection++;
      }

      console.log(
        `   ✅ Updated ${updatedInCollection} documents in ${collectionName}`,
      );
      totalUpdated += updatedInCollection;
    }

    // Step 3: Verify results
    console.log('\n🔍 Verification...');
    for (const collectionName of COLLECTIONS_WITH_CREATED_BY) {
      if (!existingCollections.includes(collectionName)) continue;

      const collection = db.collection(collectionName);

      const totalDocs = await collection.countDocuments({
        createdBy: { $exists: true },
      });
      const docsWithUsername = await collection.countDocuments({
        createdBy: { $exists: true },
        createdByUsername: { $exists: true },
      });

      console.log(
        `   ${collectionName}: ${docsWithUsername}/${totalDocs} documents have createdByUsername`,
      );
    }

    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📊 Total documents updated: ${totalUpdated}`);
  } catch (error) {
    console.error('💥 Migration failed:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Helper function to show sample data
export async function showSampleData(): Promise<void> {
  try {
    await mongoose.connect(MONGO_URI);
    const db = mongoose.connection.db;
    if (!db) return;

    console.log('\n📋 Sample data after migration:');

    const collections = await db.listCollections().toArray();
    const existingCollections = collections.map((c) => c.name);

    for (const collectionName of COLLECTIONS_WITH_CREATED_BY.slice(0, 3)) {
      if (!existingCollections.includes(collectionName)) continue;

      const collection = db.collection(collectionName);
      const sample = await collection.findOne(
        {
          createdBy: { $exists: true },
          createdByUsername: { $exists: true },
        },
        {
          projection: { createdBy: 1, createdByUsername: 1, _id: 1 },
        },
      );

      if (sample) {
        console.log(`   ${collectionName}:`, {
          _id: sample._id,
          createdBy: sample.createdBy,
          createdByUsername: sample.createdByUsername,
        });
      }
    }
  } catch (error) {
    console.error('Error showing sample data:', error);
  } finally {
    await mongoose.disconnect();
  }
}

// Get migration statistics
export async function getMigrationStats(): Promise<{
  totalCollections: number;
  processedCollections: number;
  totalDocuments: number;
  migratedDocuments: number;
  coverage: number;
}> {
  try {
    await mongoose.connect(MONGO_URI);
    const db = mongoose.connection.db;
    if (!db) throw new Error('Database connection failed');

    const collections = await db.listCollections().toArray();
    const existingCollections = collections.map((c) => c.name);

    let totalDocuments = 0;
    let migratedDocuments = 0;
    let processedCollections = 0;

    for (const collectionName of COLLECTIONS_WITH_CREATED_BY) {
      if (!existingCollections.includes(collectionName)) continue;

      processedCollections++;
      const collection = db.collection(collectionName);

      const totalDocs = await collection.countDocuments({
        createdBy: { $exists: true },
      });
      const docsWithUsername = await collection.countDocuments({
        createdBy: { $exists: true },
        createdByUsername: { $exists: true },
      });

      totalDocuments += totalDocs;
      migratedDocuments += docsWithUsername;
    }

    const coverage =
      totalDocuments > 0 ? (migratedDocuments / totalDocuments) * 100 : 100;

    return {
      totalCollections: COLLECTIONS_WITH_CREATED_BY.length,
      processedCollections,
      totalDocuments,
      migratedDocuments,
      coverage: Math.round(coverage * 100) / 100,
    };
  } catch (error) {
    console.error('Error getting migration stats:', error);
    throw error;
  } finally {
    await mongoose.disconnect();
  }
}

// Run the migration if called directly
if (require.main === module) {
  console.log('🚀 Starting createdByUsername migration...');
  console.log(`📍 MongoDB URI: ${MONGO_URI}`);
  console.log(`📍 Database: ${DB_NAME}`);
  console.log(
    `📍 Collections to process: ${COLLECTIONS_WITH_CREATED_BY.length}`,
  );
  console.log('');

  addCreatedByUsername()
    .then(() => showSampleData())
    .then(() => {
      console.log('\n✨ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}
