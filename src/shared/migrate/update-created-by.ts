import mongoose from 'mongoose';

const DB_NAME = process.env.MONGO_ADMIN_DB_NAME || 'admin0x1';
let MONGO_URI = process.env.MONGO_URI || '';
if (!MONGO_URI.endsWith(`/${DB_NAME}`)) {
  MONGO_URI = `${MONGO_URI.replace(/\/$/, '')}/${DB_NAME}`;
}

export const migrateCreatedBy = async () => {
  console.log(
    'Migrating createdBy, updatedBy, deletedBy fields in all collections...',
  );
  await mongoose.connect(MONGO_URI);

  const User = mongoose.connection.collection('users');

  // Lấy map username -> telegramId
  const users = await User.find({})
    .project({ username: 1, telegramId: 1 })
    .toArray();
  const usernameToTelegramId: Record<string, number> = {};
  users.forEach((u) => {
    if (u.username && u.telegramId) {
      usernameToTelegramId[u.username] = u.telegramId;
    }
  });

  // Lấy tất cả collection trong DB
  const collections = await mongoose.connection.db?.listCollections().toArray();

  if (!collections) {
    console.error('No collections found');
    return;
  }

  for (const col of collections) {
    // Bỏ qua collection users (không cần migrate)
    if (col.name === 'users') continue;

    const collection = mongoose.connection.collection(col.name);

    // Tìm các record có trường createdBy, updatedBy, hoặc deletedBy là string (username)
    const docs = await collection
      .find({
        $or: [
          { createdBy: { $exists: true } },
          { updatedBy: { $exists: true } },
          { deletedBy: { $exists: true } },
        ],
      })
      .toArray();

    for (const doc of docs) {
      const updateFields: Record<string, any> = {};

      if (doc.createdBy) {
        const telegramId = usernameToTelegramId[doc.createdBy];
        if (telegramId) updateFields.createdBy = telegramId;
      }
      if (doc.updatedBy) {
        const telegramId = usernameToTelegramId[doc.updatedBy];
        if (telegramId) updateFields.updatedBy = telegramId;
      }
      if (doc.deletedBy) {
        const telegramId = usernameToTelegramId[doc.deletedBy];
        if (telegramId) updateFields.deletedBy = telegramId;
      }

      if (Object.keys(updateFields).length > 0) {
        await collection.updateOne({ _id: doc._id }, { $set: updateFields });
      }
    }
    console.log(`Migrated collection: ${col.name}`);
  }

  console.log('Migration done!');
  await mongoose.disconnect();
};
