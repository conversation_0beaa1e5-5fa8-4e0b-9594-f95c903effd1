import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isNil } from 'lodash';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import {
  FileEmail,
  FileEmailDocument,
} from '../entities/file/file-email.entity';
import { getCountryCodeFromIp } from '../utils/geoip.util';

@Injectable()
export class CountryCodeGuard implements CanActivate {
  constructor(
    @InjectModel(FileEmail.name, AdminDB)
    private readonly fileEmailModel: Model<FileEmailDocument>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const shortLinkId = request.query.shortLinkId;

    if (!shortLinkId) {
      throw new UnauthorizedException('Short link ID is required.');
    }

    const fileEmail = await this.fileEmailModel
      .findOne({
        shortLinkId,
      })
      .select('countryCode');

    if (isNil(fileEmail) || isNil(fileEmail.countryCode)) {
      throw new UnauthorizedException('Short link not found.');
    }

    const allowedCountryCode = fileEmail.countryCode;

    if (allowedCountryCode || allowedCountryCode.length > 0) {
      const countryCodeQuery = request.query.countryCode;
      if (countryCodeQuery) {
        if (countryCodeQuery !== allowedCountryCode) {
          throw new UnauthorizedException(
            `Access denied from country code: ${countryCodeQuery}.`,
          );
        }
      } else {
        const clientIp = request.query.ip;
        const countryCode = getCountryCodeFromIp(clientIp);

        if (!countryCode || !clientIp) {
          throw new UnauthorizedException('Access denied.');
        }

        if (allowedCountryCode !== countryCode) {
          throw new UnauthorizedException(
            `Access denied from country code: ${countryCode}.`,
          );
        }
      }
    }

    return true;
  }
}
