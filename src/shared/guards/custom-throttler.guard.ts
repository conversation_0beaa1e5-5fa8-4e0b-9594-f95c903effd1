import { Injectable } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';

@Injectable()
export class CustomThrottlerGuard extends ThrottlerGuard {
  protected async shouldSkip(request: any): Promise<boolean> {
    const whitelistIP = process.env.WHITELIST_IP?.split(',') || []; // Get the whitelist IPs from environment variables

    const clientIp = request.ip; // Get the client's IP
    return whitelistIP.includes(clientIp); // Skip rate limiting if IP is in whitelist
  }
}
