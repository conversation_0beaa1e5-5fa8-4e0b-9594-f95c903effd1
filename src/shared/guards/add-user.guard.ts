import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';

@Injectable()
export class AddUserGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();

    const accessToken = request.headers['x-access-token'];
    const profileId = request.headers['x-profile-id'];

    // Check if both headers are present
    if (!accessToken) {
      throw new UnauthorizedException('Missing access token header');
    }
    if (!profileId) {
      throw new UnauthorizedException('Missing profile id header');
    }

    // Optionally, you can add further validation logic for the headers here
    if (
      accessToken !== process.env.ADD_ADMIN_ACCESS_TOKEN ||
      profileId !== process.env.ADD_ADMIN_PROFILE_ID
    ) {
      throw new UnauthorizedException('Invalid access token or profile ID');
    }

    return true;
  }
}
