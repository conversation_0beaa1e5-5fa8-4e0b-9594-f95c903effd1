import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as jwt from 'jsonwebtoken';
import { set } from 'lodash';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import { GroupUserConnection } from '../entities/auth/group-user-connection.entity';
import {
  ValidToken,
  ValidTokenDocument,
} from '../entities/auth/valid-token.entity';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(ValidToken.name, AdminDB)
    private validTokenModel: Model<ValidTokenDocument>,
    @InjectModel(GroupUserConnection.name, AdminDB)
    private groupUserConnectionModel: Model<GroupUserConnection>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers['authorization'];

    if (!authHeader) {
      throw new UnauthorizedException('Authorization header is missing');
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      throw new UnauthorizedException('Access token is missing');
    }

    const validToken = await this.validTokenModel.findOne({
      accessToken: token,
      expiresAt: { $gt: new Date() },
    });
    if (!validToken) {
      throw new UnauthorizedException('Invalid or expired access token');
    }

    try {
      const jwtSecret = this.configService.get<string>('JWT_SECRET');
      if (!jwtSecret) {
        throw new Error('JWT secret is not configured');
      }

      // Verify the token
      const decoded = jwt.verify(token, jwtSecret);

      // Attach the decoded user information to the request
      request.user = decoded;

      const connections = await this.groupUserConnectionModel.aggregate([
        { $match: { userId: request.user._id } },
        {
          $group: {
            _id: null,
            groupIds: { $addToSet: '$groupId' },
          },
        },
        {
          $lookup: {
            from: 'group_user_connections',
            let: { groupIds: '$groupIds' },
            pipeline: [
              { $match: { $expr: { $in: ['$groupId', '$$groupIds'] } } },
              {
                $lookup: {
                  from: 'users',
                  localField: 'userId',
                  foreignField: '_id',
                  as: 'user',
                },
              },
              { $unwind: '$user' },
              { $project: { telegramId: '$user.telegramId' } },
            ],
            as: 'groupUsers',
          },
        },
        {
          $project: {
            telegramIds: {
              $cond: [
                { $gt: [{ $size: '$groupIds' }, 0] },
                '$groupUsers.telegramId',
                [request.user.telegramId],
              ],
            },
          },
        },
      ]);

      set(request, 'user.groupUsers', connections[0]?.telegramIds || []);

      return true;
    } catch (err) {
      console.log('error', err);
      throw new UnauthorizedException('Invalid or expired access token');
    }
  }
}
