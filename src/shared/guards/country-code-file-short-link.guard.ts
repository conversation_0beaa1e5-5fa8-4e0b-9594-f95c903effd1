import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isNil } from 'lodash';
import { Model } from 'mongoose';
import { AdminDB } from 'src/config';
import {
  FileShortLinkMeta,
  FileShortLinkMetaDocument,
} from '../entities/file/file-shortlink-meta.entity';
import { getCountryCodeFromIp } from '../utils/geoip.util';

@Injectable()
export class CountryCodeFileShortLinkGuard implements CanActivate {
  constructor(
    @InjectModel(FileShortLinkMeta.name, AdminDB)
    private readonly fileShortLinkMetaModel: Model<FileShortLinkMetaDocument>,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    const shortLinkId = request.query.shortLinkId;

    if (!shortLinkId) {
      throw new UnauthorizedException('Short link ID is required.');
    }

    const fileEmail = await this.fileShortLinkMetaModel
      .findOne({
        shortLinkId,
      })
      .select('countryCode');

    if (isNil(fileEmail) || isNil(fileEmail.countryCode)) {
      throw new UnauthorizedException('Short link not found.');
    }

    const allowedCountryCode = fileEmail.countryCode;

    if (allowedCountryCode || allowedCountryCode.length > 0) {
      const countryCodeQuery = request.query.countryCode;
      if (countryCodeQuery) {
        if (countryCodeQuery !== allowedCountryCode) {
          throw new UnauthorizedException(
            `Access denied from country code: ${countryCodeQuery}.`,
          );
        }
      } else {
        const clientIp = request.query.ip;
        const countryCode = getCountryCodeFromIp(clientIp);

        if (!countryCode || !clientIp) {
          throw new UnauthorizedException('Access denied.');
        }

        if (allowedCountryCode !== countryCode) {
          throw new UnauthorizedException(
            `Access denied from country code: ${countryCode}.`,
          );
        }
      }
    }

    return true;
  }
}
