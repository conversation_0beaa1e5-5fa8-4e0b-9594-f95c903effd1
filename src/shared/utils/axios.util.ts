import axios, {
  AxiosAdapter,
  AxiosBasicCredentials,
  AxiosInstance,
  AxiosProxyConfig,
  AxiosRequestConfig, // Use AxiosRequestConfig from axios
  AxiosRequestHeaders,
  AxiosRequestTransformer,
  AxiosResponseTransformer,
  CancelToken,
  Method,
  TransitionalOptions,
} from 'axios';
import { set } from 'lodash';

/**
 * create a new instance
 * @param config axios configuration
 */
export function createAxiosInstance(config: AxiosRequestConfig): AxiosInstance {
  const instance = axios.create(config);
  instance.interceptors.request.use(function (config) {
    Object.keys(axios.defaults.headers.common).forEach((key) => {
      set(config, `headers.${key}`, axios.defaults.headers.common[key]);
    });
    return config;
  });
  return instance;
}

export const x54Api = (): AxiosInstance => {
  return createAxiosInstance({
    baseURL: process.env.X54_API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
    timeout: 10000,
  });
};
