import { Model, Query, Schema } from 'mongoose';
import { ROLE } from 'src/shared/constants/auth.constant';
import { User } from 'src/shared/entities/auth/user.entity';

export interface AuthModel<T> extends Model<T> {
  findWithAuth(query: any, authUser: User): Query<T[], T>;
  findOneWithAuth(query: any, authUser: User): Query<T | null, T>;
  findByIdWithAuth(id: string, authUser: User): Query<T | null, T>;
  countDocumentsWithAuth(query: any, authUser: User): Query<number, T>;
  aggregateWithAuth(pipeline: any[], authUser: User): Query<any[], any>;
  findOneAndUpdateWithAuth(
    filter: any,
    update: any,
    option: any,
    authUser: User,
  ): Promise<T | null>;
  updateOneWithAuth(
    filter: any,
    update: any,
    authUser: User,
  ): Promise<{ nModified?: number }>;
}

function getCreatedByFilter(authUser: any) {
  if (Array.isArray(authUser.groupUsers) && authUser.groupUsers.length > 0) {
    return { createdBy: { $in: authUser.groupUsers } };
  }
  return { createdBy: authUser.telegramId };
}

export function addAuthStaticMethods(schema: Schema) {
  schema.statics.findWithAuth = function (query: any, authUser: User) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      query = { ...query, ...getCreatedByFilter(authUser) };
    }
    return this.find(query);
  };

  schema.statics.findOneWithAuth = function (query: any, authUser: User) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      query = { ...query, ...getCreatedByFilter(authUser) };
    }
    return this.findOne(query);
  };

  schema.statics.findByIdWithAuth = function (id: string, authUser: User) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      return this.findOne({ _id: id, ...getCreatedByFilter(authUser) });
    }
    return this.findById(id);
  };

  schema.statics.countDocumentsWithAuth = function (
    query: any,
    authUser: User,
  ) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      query = { ...query, ...getCreatedByFilter(authUser) };
    }
    return this.countDocuments(query).exec();
  };

  schema.statics.aggregateWithAuth = function (
    pipeline: any[],
    authUser: User,
  ) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      const matchStage = getCreatedByFilter(authUser);
      if (pipeline[0] && pipeline[0].$match) {
        pipeline[0].$match = { ...pipeline[0].$match, ...matchStage };
      } else {
        pipeline.unshift({ $match: matchStage });
      }
    }
    return this.aggregate(pipeline);
  };

  schema.statics.findOneAndUpdateWithAuth = function (
    filter: any,
    update: any,
    option: any,
    authUser: User,
  ) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      filter = { ...filter, ...getCreatedByFilter(authUser) };
    }
    return this.findOneAndUpdate(filter, update, option);
  };

  schema.statics.updateOneWithAuth = function (
    filter: any,
    update: any,
    authUser: User,
  ) {
    if (![ROLE.ADMIN, ROLE.SUPER_ADMIN].includes(authUser.role)) {
      filter = { ...filter, ...getCreatedByFilter(authUser) };
    }
    return this.updateOne(filter, update);
  };
}
