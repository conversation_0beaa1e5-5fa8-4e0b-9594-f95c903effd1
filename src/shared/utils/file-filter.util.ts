import { BadRequestException } from '@nestjs/common';

export const fileFilter = (
  req: any,
  file: Express.Multer.File,
  callback: Function,
) => {
  if (
    !file.mimetype.match(
      /\/(pdf|msword|vnd.openxmlformats-officedocument.wordprocessingml.document)$/,
    )
  ) {
    return callback(
      new BadRequestException('Only PDF and DOC/DOCX files are allowed!'),
      false,
    );
  }
  callback(null, true);
};
