import { groupBy, flatten } from 'lodash';

export const findDuplicates = (
  items: Record<string, any>[],
  compareKey: string,
): {
  duplicates: any[];
  uniqueItems: any[];
} => {
  // Nhóm các mục theo giá trị của `compareKey`
  const grouped = groupBy(items, compareKey);

  // Lấy các mục trùng lặp (có nhiều hơn 1 phần tử trong nhóm)
  const duplicates = flatten(
    Object.values(grouped).filter((group) => group.length > 1),
  );

  // Lấy các mục duy nhất (chỉ có 1 phần tử trong nhóm)
  const uniqueItems = flatten(
    Object.values(grouped).filter((group) => group.length === 1),
  );

  return { duplicates, uniqueItems };
};

export const normalizeDomain = (searchInput: string): string => {
  return searchInput
    .replace(/^(https?:\/\/)/, '') // Loại bỏ http:// hoặc https://
    .replace(/\/$/, ''); // Loại bỏ dấu / ở cuối
};

export function extractSubdomain(input: string): string {
  let domain = input.replace(/^(https?:\/\/)/, '');
  domain = domain.split('/')[0];

  const parts = domain.split('.');

  if (parts.length >= 3) {
    return parts[0];
  }

  return input;
}

export const parseDate = (dateString: string): Date | null => {
  const [day, month, year] = dateString.split('-').map(Number);
  if (!day || !month || !year) {
    return null;
  }
  return new Date(Date.UTC(year, month - 1, day));
};
