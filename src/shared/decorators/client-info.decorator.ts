import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import * as geoip from 'geoip-lite';

export interface ClientInfoData {
  ip: string;
  userAgent: string;
  country: string;
}

export const ClientInfo = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ClientInfoData => {
    const request = ctx.switchToHttp().getRequest<Request>();

    // Extract IP and User-Agent
    const ip =
      (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      request.ip ||
      'Unknown';
    const userAgent = request.headers['user-agent'] || 'Unknown';

    // Lookup country using geoip-lite
    let geo: geoip.Lookup | null = null;
    if (ip) {
      geo = geoip.lookup(ip);
    }
    const country = geo ? geo.country : 'Unknown'; // Country code (e.g., "US")

    return { ip, userAgent, country };
  },
);
