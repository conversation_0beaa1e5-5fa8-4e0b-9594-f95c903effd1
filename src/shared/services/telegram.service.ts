import { Injectable } from '@nestjs/common';
import { Telegraf } from 'telegraf';

@Injectable()
export class NotificationService {
  private botNotifyUser: Telegraf;

  constructor() {
    const tokenNotifyUser = process.env.TELEGRAM_BOT_TOKEN_NOTIFY_USER || '';
    this.botNotifyUser = new Telegraf(tokenNotifyUser);
  }

  async sendNotificationToGroup(message: string) {
    const groupId = process.env.TELEGRAM_GROUP_NOTIFY_ID || '';
    if (!groupId) {
      console.error('Group ID chưa được cấu hình.');
      return;
    }
    try {
      await this.botNotifyUser.telegram.sendMessage(groupId, message, {
        parse_mode: 'Markdown',
      });
    } catch (err) {
      console.error('<PERSON><PERSON><PERSON> thông báo đến nhóm thất bại:', err);
    }
  }
}