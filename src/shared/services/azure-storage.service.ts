import {
  BlobServiceClient,
  BlockBlobClient,
  ContainerClient,
} from '@azure/storage-blob';
import { BadRequestException, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AzureStorageService {
  private readonly containerClient: ContainerClient;

  constructor(private configService: ConfigService) {
    const connectionString = this.configService.get<string>(
      'AZURE_STORAGE_CONNECTION_STRING',
    );
    const containerName = this.configService.get<string>(
      'AZURE_STORAGE_CONTAINER_NAME',
    );

    if (!connectionString || !containerName) {
      throw new Error('Azure Storage configuration is missing');
    }

    const blobServiceClient =
      BlobServiceClient.fromConnectionString(connectionString);
    this.containerClient = blobServiceClient.getContainerClient(containerName);
  }

  async uploadFile(file: Express.Multer.File): Promise<string> {
    if (!file) {
      throw new Error('File is required');
    }

    const containerName = this.containerClient.containerName;
    const containerExists =
      await this.createContainerIfNotExists(containerName);
    if (!containerExists) {
      throw new Error(`Container ${containerName} does not exist`);
    }

    const blobName = `${Date.now()}-${file.originalname}`; // Unique blob name
    const blockBlobClient: BlockBlobClient =
      this.containerClient.getBlockBlobClient(blobName);

    await blockBlobClient.uploadData(file.buffer, {
      blobHTTPHeaders: { blobContentType: file.mimetype },
    });

    return blockBlobClient.url;
  }

  async createContainerIfNotExists(containerName: string): Promise<boolean> {
    const exists = await this.containerClient.exists();

    if (!exists) {
      console.log(`Creating container: ${containerName}`);
      const createResult = await this.containerClient.create();
      console.log(`Container created successfully: ${createResult.requestId}`);
      return true;
    }

    console.log(`Container already exists: ${containerName}`);
    return true;
  }

  async downloadFile(
    url: string,
  ): Promise<{ stream: NodeJS.ReadableStream; contentType: string }> {
    try {
      const blobName = url.split('/').pop();
      if (!blobName) {
        throw new Error('Invalid file URL');
      }

      const blockBlobClient = this.containerClient.getBlockBlobClient(blobName);
      const properties = await blockBlobClient.getProperties();
      const downloadResponse = await blockBlobClient.download();

      if (!downloadResponse.readableStreamBody || !properties.contentType) {
        console.log(
          'Failed to download file or get content type',
          downloadResponse,
          properties,
        );
        throw new Error('Failed to download file');
      }

      return {
        stream: downloadResponse.readableStreamBody,
        contentType: properties.contentType,
      };
    } catch (error) {
      console.log('Error downloading file in azure service', error);
      throw new BadRequestException('Failed to download file', error.message);
    }
  }
}
