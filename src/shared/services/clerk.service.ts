import { Injectable, NotFoundException } from '@nestjs/common';
import { createClerkClient } from '@clerk/clerk-sdk-node';

interface ClerkCreateUserParams {
  emailAddress?: string[];
  phoneNumber?: string[];
  username?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
  externalId?: string;
  skipPasswordChecks?: boolean;
  skipPasswordRequirement?: boolean;
}

@Injectable()
export class ClerkUserService {
  private readonly clerkClient;

  constructor() {
    const clerkSecretKey = process.env.CLERK_SECRET_KEY;

    if (!clerkSecretKey) {
      throw new Error('Clerk secret key is not configured');
    }

    this.clerkClient = createClerkClient({
      secretKey: clerkSecretKey,
    });
  }

  /**
   * Tạo một người dùng mới trong Clerk
   * @param userData Thông tin người dùng cần tạo
   * @returns Thông tin người dùng đã tạo từ Clerk
   */
  async createUser(userData: ClerkCreateUserParams) {
    try {
      const createdUser = await this.clerkClient.users.createUser(userData);
      return createdUser;
    } catch (error) {
      console.error('Failed to create user in Clerk:', error);
      // throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  /**
   * Xóa người dùng trong Clerk theo email
   * @param email Email của người dùng cần xóa
   * @returns Thông báo thành công hoặc lỗi nếu không tìm thấy
   */
  async deleteUserByEmail(email: string) {
    try {
      const users = await this.clerkClient.users.getUserList({
        emailAddress: [email],
      });

      if (!users.data || users.data.length === 0) {
        throw new NotFoundException(`No user clerk found with email: ${email}`);
      }

      const userId = users.data[0].id;

      await this.clerkClient.users.deleteUser(userId);

      return {
        success: true,
        message: `User with email ${email} deleted successfully`,
      };
    } catch (error) {
      console.error('Failed to delete user in Clerk:', error);
      // throw new Error(`Failed to delete user: ${error.message}`);
    }
  }
}
