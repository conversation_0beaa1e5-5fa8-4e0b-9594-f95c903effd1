export enum CAMP_STATUS {
  PENDING = 'pending',
  OPENED = 'open',
  SENT = 'sent',
}

export enum EMAIL_STATUS {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  OPENED = 'opened',
  CLICKED = 'clicked',
  BOUNCED = 'bounced',
  FAILED = 'failed',
  UNSUBSCRIBED = 'unsubscribed',
}

export enum CAMP_USER_STATUS {
  ACTIVE = 'active',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  UNSUBSCRIBED = 'unsubscribed',
}

export enum EMAIL_TYPE {
  START = 'start',
  END = 'end',
}

// Helper function để tạo dynamic other email types
export const createOtherEmailType = (index: number): string => {
  return `other_${index}`;
};

// Helper function để validate email type
export const isValidEmailType = (emailType: string): boolean => {
  if (emailType === EMAIL_TYPE.START || emailType === EMAIL_TYPE.END) {
    return true;
  }
  return /^other_\d+$/.test(emailType);
};
