variables:
  TELEGRAM_CHAT_ID: "-1002312293687_"
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

stages:
  - deploy

.start_template: &start_notification
  - |
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
      -d "chat_id=${TELEGRAM_CHAT_ID}" \
      -d "parse_mode=HTML" \
      -d "text=🚀 <b>BẮT ĐẦU ${CI_JOB_STAGE^^}</b>
    <b>Project:</b> ${CI_PROJECT_NAME}
    <b>Branch:</b> ${CI_COMMIT_REF_NAME}
    <b>Stage:</b> ${CI_JOB_STAGE}
    <b>Job:</b> ${CI_JOB_NAME}
    <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
    <b>Author:</b> ${GITLAB_USER_NAME}
    <b>Th<PERSON>i gian:</b> $(date '+%Y-%m-%d %H:%M:%S')"

.success_template: &success_notification
  - |
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
      -d "chat_id=${TELEGRAM_CHAT_ID}" \
      -d "parse_mode=HTML" \
      -d "text=✅ <b>${CI_JOB_STAGE^^} THÀNH CÔNG</b>
    <b>Project:</b> ${CI_PROJECT_NAME}
    <b>Branch:</b> ${CI_COMMIT_REF_NAME}
    <b>Stage:</b> ${CI_JOB_STAGE}
    <b>Job:</b> ${CI_JOB_NAME}
    <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
    <b>Author:</b> ${GITLAB_USER_NAME}
    <b>Thời gian hoàn thành:</b> $(date '+%Y-%m-%d %H:%M:%S')"

.cleanup_docker: &cleanup_docker
  - echo "🧹 Cleaning up Docker system..."
  - docker image prune -a --filter "until=168h" --force || true
  - docker builder prune --filter "until=168h" --keep-storage=5 --force || true
  - docker system df

# Deploy dev
deploy-dev:
  stage: deploy
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env
  tags:
    - api-dev-runner
  only:
    - dev
  before_script:
    - *start_notification
    - *cleanup_docker
  script:
    - set -o pipefail
    - cp "$ENV_PATH" .env
    - echo "🛠️ Building docker image..."
    - DOCKER_BUILDKIT=1 docker compose build 2>&1 | tee /tmp/job_log.txt
    - echo "🚀 Deploying container..."
    - docker compose down 2>&1 | tee -a /tmp/job_log.txt || true
    - docker compose up -d 2>&1 | tee -a /tmp/job_log.txt
    - *success_notification
    - *cleanup_docker
  after_script:
    - |
      if [ "$CI_JOB_STATUS" != "success" ]; then
        ERROR_LOG=$(tail -n 20 /tmp/job_log.txt 2>/dev/null || echo "Không thể truy cập log")
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -d "chat_id=${TELEGRAM_CHAT_ID}" \
          -d "parse_mode=HTML" \
          -d "text=❌ <b>${CI_JOB_STAGE^^} THẤT BẠI</b>
        <b>Project:</b> ${CI_PROJECT_NAME}
        <b>Branch:</b> ${CI_COMMIT_REF_NAME}
        <b>Stage:</b> ${CI_JOB_STAGE}
        <b>Job:</b> ${CI_JOB_NAME}
        <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
        <b>Author:</b> ${GITLAB_USER_NAME}
        <b>Thời gian thất bại:</b> $(date '+%Y-%m-%d %H:%M:%S')
        <b>20 dòng log lỗi cuối cùng:</b>
        <pre>${ERROR_LOG}</pre>"
      fi

# Deploy production
deploy-production:
  stage: deploy
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env.production
  tags:
    - api-prod-runner
  only:
    - production
  before_script:
    - *start_notification
    - *cleanup_docker
  script:
    - set -o pipefail
    - cp "$ENV_PATH" .env
    - echo "🛠️ Building docker image..."
    - DOCKER_BUILDKIT=1 docker compose build 2>&1 | tee /tmp/job_log.txt
    - echo "🚀 Deploying container..."
    - docker compose down 2>&1 | tee -a /tmp/job_log.txt || true
    - docker compose up -d 2>&1 | tee -a /tmp/job_log.txt
    - *success_notification
    - *cleanup_docker
  after_script:
    - |
      if [ "$CI_JOB_STATUS" != "success" ]; then
        ERROR_LOG=$(tail -n 20 /tmp/job_log.txt 2>/dev/null || echo "Không thể truy cập log")
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -d "chat_id=${TELEGRAM_CHAT_ID}" \
          -d "parse_mode=HTML" \
          -d "text=❌ <b>${CI_JOB_STAGE^^} THẤT BẠI</b>
        <b>Project:</b> ${CI_PROJECT_NAME}
        <b>Branch:</b> ${CI_COMMIT_REF_NAME}
        <b>Stage:</b> ${CI_JOB_STAGE}
        <b>Job:</b> ${CI_JOB_NAME}
        <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
        <b>Author:</b> ${GITLAB_USER_NAME}
        <b>Thời gian thất bại:</b> $(date '+%Y-%m-%d %H:%M:%S')
        <b>20 dòng log lỗi cuối cùng:</b>
        <pre>${ERROR_LOG}</pre>"
      fi
