#!/bin/bash

# <PERSON><PERSON><PERSON> ngh<PERSON>a màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Hàm hiển thị thông báo
echo_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

echo_error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

# Dừng và xóa các container hiện tại
echo_info "Stopping and removing current containers..."
docker-compose -f docker-compose-prod.yml down
if [ $? -ne 0 ]; then
    echo_error "Failed to stop containers"
    exit 1
fi

# Pull các image mới nhất
echo_info "Pulling latest images..."
docker-compose -f docker-compose-prod.yml pull
if [ $? -ne 0 ]; then
    echo_error "Failed to pull images"
    exit 1
fi

# Khởi động các container
echo_info "Starting containers..."
docker-compose -f docker-compose-prod.yml up -d
if [ $? -ne 0 ]; then
    echo_error "Failed to start containers"
    exit 1
fi

# Dọn dẹp các image không sử dụng
echo_info "Pruning unused images..."
docker image prune -f
if [ $? -ne 0 ]; then
    echo_error "Failed to prune images"
    exit 1
fi

echo_info "Deployment completed successfully!"