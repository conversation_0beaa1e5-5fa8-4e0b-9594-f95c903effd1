variables:
  TELEGRAM_CHAT_ID: "-1002312293687"
  DOCKER_BUILDKIT: 1  # K<PERSON>ch hoạt BuildKit để tăng tốc build và cải thiện cache
  COMPOSE_DOCKER_CLI_BUILD: 1  # Sử dụng Docker CLI với chức năng xây dựng

stages:
  - build
  - deploy

.start_template: &start_notification
  - |
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
      -d "chat_id=${TELEGRAM_CHAT_ID}" \
      -d "parse_mode=HTML" \
      -d "text=🚀 <b>BẮT ĐẦU ${CI_JOB_STAGE^^}</b>
    <b>Project:</b> ${CI_PROJECT_NAME}
    <b>Branch:</b> ${CI_COMMIT_REF_NAME}
    <b>Stage:</b> ${CI_JOB_STAGE}
    <b>Job:</b> ${CI_JOB_NAME}
    <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
    <b>Author:</b> ${GITLAB_USER_NAME}
    <b>Thời gian:</b> $(date '+%Y-%m-%d %H:%M:%S')"

.success_template: &success_notification
  - |
    curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
      -d "chat_id=${TELEGRAM_CHAT_ID}" \
      -d "parse_mode=HTML" \
      -d "text=✅ <b>${CI_JOB_STAGE^^} THÀNH CÔNG</b>
    <b>Project:</b> ${CI_PROJECT_NAME}
    <b>Branch:</b> ${CI_COMMIT_REF_NAME}
    <b>Stage:</b> ${CI_JOB_STAGE}
    <b>Job:</b> ${CI_JOB_NAME}
    <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
    <b>Author:</b> ${GITLAB_USER_NAME}
    <b>Thời gian hoàn thành:</b> $(date '+%Y-%m-%d %H:%M:%S')"

# Common docker cleanup template
.cleanup_docker: &cleanup_docker
  # Dọn dẹp image không sử dụng và giữ lại 5 bản gần nhất
  - echo "🧹 Cleaning up Docker system..."
  - docker image prune -a --filter "until=168h" --force || true
  - docker builder prune --filter "until=168h" --keep-storage=5 --force || true
  - docker system df  # Hiển thị thông tin sử dụng disk sau khi dọn dẹp


# Template chung cho build job
.build_job: &build_job_template
  stage: build
  before_script:
    - *start_notification
  script:
    - set -o pipefail
    - cp "$ENV_PATH" .env
    - docker builder prune --keep-storage=5 --force
    - docker compose build 2>&1 | tee /tmp/job_log.txt
    - *success_notification
  after_script:
    - |
      if [ "$CI_JOB_STATUS" != "success" ]; then
        # Lưu log lỗi vào biến
        ERROR_LOG=$(tail -n 20 /tmp/job_log.txt 2>/dev/null || echo "Không thể truy cập log")
        
        # Gửi thông báo lỗi kèm log
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -d "chat_id=${TELEGRAM_CHAT_ID}" \
          -d "parse_mode=HTML" \
          -d "text=❌ <b>${CI_JOB_STAGE^^} THẤT BẠI</b>
        <b>Project:</b> ${CI_PROJECT_NAME}
        <b>Branch:</b> ${CI_COMMIT_REF_NAME}
        <b>Stage:</b> ${CI_JOB_STAGE}
        <b>Job:</b> ${CI_JOB_NAME}
        <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
        <b>Author:</b> ${GITLAB_USER_NAME}
        <b>Thời gian thất bại:</b> $(date '+%Y-%m-%d %H:%M:%S')
        
        <b>20 dòng log lỗi cuối cùng:</b>
        <pre>${ERROR_LOG}</pre>"
      fi

# Template chung cho deploy job
.deploy_job: &deploy_job_template
  stage: deploy
  before_script:
    - *start_notification
  script:
    - set -o pipefail
    - cp "$ENV_PATH" .env
    - docker compose down 2>&1 | tee /tmp/job_log.txt
    - docker compose up -d 2>&1 | tee -a /tmp/job_log.txt
    - *success_notification
  after_script:
    - |
      if [ "$CI_JOB_STATUS" != "success" ]; then
        # Lưu log lỗi vào biến
        ERROR_LOG=$(tail -n 20 /tmp/job_log.txt 2>/dev/null || echo "Không thể truy cập log")
        
        # Gửi thông báo lỗi kèm log
        curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" \
          -d "chat_id=${TELEGRAM_CHAT_ID}" \
          -d "parse_mode=HTML" \
          -d "text=❌ <b>${CI_JOB_STAGE^^} THẤT BẠI</b>
        <b>Project:</b> ${CI_PROJECT_NAME}
        <b>Branch:</b> ${CI_COMMIT_REF_NAME}
        <b>Stage:</b> ${CI_JOB_STAGE}
        <b>Job:</b> ${CI_JOB_NAME}
        <b>Commit:</b> ${CI_COMMIT_SHORT_SHA} - ${CI_COMMIT_MESSAGE}
        <b>Author:</b> ${GITLAB_USER_NAME}
        <b>Thời gian thất bại:</b> $(date '+%Y-%m-%d %H:%M:%S')
        
        <b>20 dòng log lỗi cuối cùng:</b>
        <pre>${ERROR_LOG}</pre>"
      fi
  when: on_success

# Build job cho môi trường dev
build-dev:
  <<: *build_job_template
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env
  tags:
    - api-dev-runner
  only:
    - dev

# Deploy job cho môi trường dev
deploy-dev:
  <<: *deploy_job_template
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env
  tags:
    - api-dev-runner
  only:
    - dev
  dependencies:
    - build-dev

# Build job cho môi trường production
build-production:
  <<: *build_job_template
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env.production
  tags:
    - api-prod-runner
  only:
    - production

# Deploy job cho môi trường production
deploy-production:
  <<: *deploy_job_template
  variables:
    ENV_PATH: /home/<USER>/env/admin-0x1-api/.env.production
  tags:
    - api-prod-runner
  only:
    - production
  dependencies:
    - build-production
