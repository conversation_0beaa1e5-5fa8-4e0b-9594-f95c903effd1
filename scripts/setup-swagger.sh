#!/bin/bash

# Swagger Setup Script for Admin 0x1 API

echo "🚀 Setting up Swagger for Admin 0x1 API..."

# Check if package.json exists
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if yarn is available
if command -v yarn &> /dev/null; then
    echo "📦 Installing dependencies with yarn..."
    yarn add @nestjs/swagger swagger-ui-express
    
    # Install dev dependencies for types
    yarn add -D @types/swagger-ui-express
else
    echo "📦 Installing dependencies with npm..."
    npm install @nestjs/swagger swagger-ui-express
    
    # Install dev dependencies for types
    npm install -D @types/swagger-ui-express
fi

# Check if installation was successful
if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Error installing dependencies. Please check the error messages above."
    exit 1
fi

echo ""
echo "🎉 Swagger setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Start the application: npm run start:dev or yarn start:dev"
echo "2. Open your browser and go to: http://localhost:3000/api/docs"
echo "3. Use the interactive API documentation to test endpoints"
echo ""
echo "📚 For more information, see SWAGGER_SETUP.md"
