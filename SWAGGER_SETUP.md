# Swagger API Documentation Setup

## Overview

Swagger API documentation has been successfully set up for the Admin 0x1 API project. This provides interactive API documentation that allows developers to explore and test the API endpoints directly from the browser.

## Installation

To complete the setup, you need to install the required dependencies:

```bash
# Using npm
npm install @nestjs/swagger swagger-ui-express

# Using yarn
yarn add @nestjs/swagger swagger-ui-express
```

## Access Swagger Documentation

Once the application is running in development mode, you can access the Swagger documentation at:

```
http://localhost:3000/api/docs
```

**Note**: Swagger is only available in development mode (when `NODE_ENV !== 'production'`).

## Features Implemented

### 1. Main Setup
- ✅ Swagger configuration in `src/main.ts`
- ✅ JWT Bearer authentication setup
- ✅ API versioning and metadata

### 2. Users Module Documentation
- ✅ Complete API documentation for all endpoints
- ✅ Request/Response DTOs with examples
- ✅ Error response schemas
- ✅ Authentication and authorization documentation

### 3. Enhanced DTOs
- ✅ Fixed type issues in `UserResponseDTO` (lastName and username now correctly typed as string)
- ✅ Added comprehensive Swagger decorators
- ✅ Improved validation with proper types and constraints
- ✅ Added examples and descriptions for all fields

### 4. Error Handling Documentation
- ✅ Standardized error response DTOs
- ✅ Documented all possible error scenarios
- ✅ Proper HTTP status codes

## API Endpoints Documented

### Users Controller (`/api/users`)

1. **GET /api/users** - Get users list
   - Query parameters: skip, limit, username, roles
   - Pagination support
   - Role-based filtering

2. **PATCH /api/users/:id** - Update user
   - Update user role and credit
   - Permission checks for role changes

3. **POST /api/users/:id/charge-credit** - Charge user credit
   - Deduct credit from user account
   - Validation for credit amounts

4. **POST /api/users/add-whitelist** - Add users to whitelist
   - Bulk add usernames to whitelist
   - Duplicate handling

## Authentication

All endpoints require JWT authentication. To test the API:

1. Obtain a JWT token through your authentication endpoint
2. Click the "Authorize" button in Swagger UI
3. Enter your JWT token in the format: `Bearer <your-token>`
4. All subsequent requests will include the authorization header

## DTO Improvements Made

### GetUsersQueryDTO
- Changed `skip` and `limit` from string to number with proper validation
- Added enum validation for roles
- Added comprehensive examples and descriptions

### UserResponseDTO
- Fixed `lastName` and `username` types (were incorrectly typed as number)
- Added proper API documentation for all fields
- Added examples for better understanding

### UpdateUserDTO
- Added enum validation for roles
- Improved validation constraints
- Added detailed descriptions

### Error Response DTOs
- Created standardized error response schemas
- Added validation error handling
- Documented all HTTP status codes

## Best Practices Implemented

1. **Consistent Documentation**: All endpoints have proper descriptions and examples
2. **Type Safety**: Fixed type issues and added proper validation
3. **Error Handling**: Comprehensive error response documentation
4. **Security**: JWT authentication properly documented
5. **Validation**: Enhanced validation with proper constraints and error messages

## Next Steps

1. Install the required dependencies
2. Start the application in development mode
3. Visit the Swagger documentation URL
4. Test the API endpoints using the interactive interface

## Troubleshooting

If you encounter issues:

1. Ensure all dependencies are installed
2. Check that the application is running in development mode
3. Verify the port number (default: 3000)
4. Check console for any startup errors

## Additional Notes

- The Swagger UI includes persistent authorization, so you only need to authenticate once per session
- All DTOs include proper examples for easy testing
- Error responses are properly documented for better debugging
- The documentation is automatically generated from the code annotations
