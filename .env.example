# Auth
JWT_SECRET_REFRESH=
JWT_SECRET=
JWT_REFRESH_EXPIRES_IN=
JWT_ACCESS_EXPIRES_IN=
PASSWORD_SALT=

# Auth Integration
ADD_ADMIN_ACCESS_TOKEN=
ADD_ADMIN_PROFILE_ID=
ADD_EMAILS_ACCESS_TOKEN=
ADD_EMAILS_PROFILE_ID=

# Web Admin Base URL
WEB_ADMIN_BASE_URL=

# Telegram bot token
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_TOKEN_NOTIFY_USER=
TELEGRAM_GROUP_NOTIFY_ID=-

# MONGO CONFIG
MONGO_URI=
# MONGO_URI=mongodb://localhost:27017
MONGO_LOG_DB_NAME=
MONGO_ADMIN_DB_NAME=

#Azure 
AZURE_STORAGE_CONNECTION_STRING=
AZURE_STORAGE_CONTAINER_NAME=api-0x1-data

#Access code to create new user
ACCESS_CODE=

WHITELIST_IP=

# x54 
X54_API_BASE_URL=
